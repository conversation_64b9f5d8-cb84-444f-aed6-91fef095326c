#!/usr/bin/env python3
"""
Test script for clean Telugu query with capital word preservation.
"""

import subprocess
import json
import os

def test_clean_telugu_query():
    """Test clean Telugu query with capital words"""
    
    print("🧪 Testing Clean Telugu Query with Capital Words")
    print("=" * 60)
    
    # Test with a cleaner Telugu query that includes capital words
    test_queries = [
        "NASA గురించి తెలియజేయండి",  # "Tell me about NASA"
        "API అంటే ఏమిటి?",  # "What is API?"
        "SpaceX కంపెనీ గురించి వివరించండి",  # "Describe SpaceX company"
        "COVID-19 వ్యాధి గురించి చెప్పండి"  # "Tell me about COVID-19 disease"
    ]
    
    script_path = os.path.join(os.path.dirname(__file__), 'services', 'google_translate_script.js')
    
    for i, query in enumerate(test_queries, 1):
        print(f"🔬 Test {i}: {query}")
        print("-" * 40)
        
        try:
            result = subprocess.run([
                'node', script_path, 'translate',
                query, 'en', 'auto'
            ], capture_output=True, text=True, encoding='utf-8', timeout=30)
            
            if result.returncode == 0 and result.stdout:
                response = json.loads(result.stdout)
                
                translated_text = response.get('translatedText', '')
                capital_words = response.get('capitalWordsPreserved', [])
                
                print(f"✅ Translation successful")
                print(f"   Original: {query}")
                print(f"   Translated: {translated_text}")
                print(f"   Capital words preserved: {capital_words}")
                
                # Check for CAPWORD tokens
                if 'CAPWORD' in translated_text or '__capital_word_' in translated_text:
                    print("❌ CAPWORD tokens found!")
                else:
                    print("✅ No CAPWORD tokens found")
                
                # Check if capital words are preserved
                expected_words = []
                if 'NASA' in query:
                    expected_words.append('NASA')
                if 'API' in query:
                    expected_words.append('API')
                if 'SpaceX' in query:
                    expected_words.append('SpaceX')
                if 'COVID-19' in query:
                    expected_words.append('COVID-19')
                
                for word in expected_words:
                    if word in translated_text:
                        print(f"✅ Capital word '{word}' preserved in translation")
                    else:
                        print(f"⚠️ Capital word '{word}' not found in translation")
                        
            else:
                print(f"❌ Translation failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()

if __name__ == "__main__":
    test_clean_telugu_query()
