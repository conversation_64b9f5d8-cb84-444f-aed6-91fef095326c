#!/usr/bin/env python3
"""
Test script to verify file name extraction from financial_query endpoint
"""

import requests
import json

def test_financial_query_with_file_names():
    """Test the financial_query endpoint to verify file name extraction"""
    
    # Test endpoint URL
    url = "http://localhost:5000/financial_query"
    
    # Test data
    test_data = {
        "query": "What is the altitude pressure suit information?",
        "index_name": "demo",  # Replace with your actual index name
        "api_key": "your_api_key_here",  # Replace with your actual API key
        "client_email": "<EMAIL>",  # Replace with actual email if needed
        "language": "English",
        "target_language": "English"
    }
    
    print("🧪 Testing financial_query endpoint for file name extraction...")
    print(f"📤 Request URL: {url}")
    print(f"📤 Request Data: {json.dumps(test_data, indent=2)}")
    
    try:
        # Make the request
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"\n📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            print("✅ Request successful!")
            print(f"\n📊 Response Summary:")
            print(f"   - Query: {response_data.get('query', 'N/A')}")
            print(f"   - Index Used: {response_data.get('index_used', 'N/A')}")
            print(f"   - AI Response Length: {len(response_data.get('ai_response', ''))}")
            
            # Check source files
            source_files = response_data.get('source_files', [])
            source_file_details = response_data.get('source_file_details', [])
            
            print(f"\n📄 SOURCE FILES EXTRACTED:")
            if source_files:
                for i, file_name in enumerate(source_files, 1):
                    print(f"   {i}. {file_name}")
            else:
                print("   ⚠️ No source files found")
            
            print(f"\n📋 SOURCE FILE DETAILS:")
            if source_file_details:
                for i, file_info in enumerate(source_file_details, 1):
                    print(f"   {i}. File: {file_info.get('file_name', 'Unknown')}")
                    print(f"      - ID: {file_info.get('file_id', 'Unknown')}")
                    print(f"      - Extension: {file_info.get('file_extension', 'unknown')}")
                    print(f"      - Score: {file_info.get('score', 0)}%")
            else:
                print("   ⚠️ No source file details found")
            
            # Check sentence analysis
            sentence_analysis = response_data.get('sentence_analysis', [])
            print(f"\n🔍 SENTENCE ANALYSIS:")
            if sentence_analysis:
                for i, sentence_data in enumerate(sentence_analysis[:3], 1):  # Show first 3 sentences
                    print(f"   Sentence {i}: {sentence_data.get('sentence', 'N/A')[:50]}...")
                    print(f"      - File Name: {sentence_data.get('file_name', 'Unknown')}")
                    print(f"      - File Extension: {sentence_data.get('file_extension', 'unknown')}")
                    print(f"      - Source Type: {sentence_data.get('source_type_display', 'Unknown')}")
                    print(f"      - Page: {sentence_data.get('page', 'Unknown')}")
                    print(f"      - File ID: {sentence_data.get('file_id', 'Unknown')}")
                    print()
                
                if len(sentence_analysis) > 3:
                    print(f"   ... and {len(sentence_analysis) - 3} more sentences")
            else:
                print("   ⚠️ No sentence analysis found")
            
            print(f"\n✅ File name extraction test completed successfully!")
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_financial_query_with_file_names()