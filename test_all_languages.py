#!/usr/bin/env python3
"""
Test all four languages (Tamil, Telugu, Kannada, Oriya) with the integrated backend
"""

import requests
import json

def test_language_integration(query, language_name, expected_lang_code):
    """Test a specific language query"""
    try:
        print(f"\n🧪 Testing {language_name} Integration")
        print(f"Query: {query}")
        print("-" * 40)
        
        response = requests.post(
            "http://localhost:5010/api/multilingual_financial_query",
            json={"query": query},
            headers={"Content-Type": "application/json"},
            timeout=45
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Status: SUCCESS")
            print(f"🌍 Detected Language: {data.get('processing_language', 'Unknown')}")
            print(f"📊 Success Flag: {data.get('success', False)}")
            
            # Check AI response
            ai_response = data.get('ai_response', '')
            if ai_response:
                print(f"💬 AI Response Preview: {ai_response[:150]}...")
            else:
                print("⚠️ No AI response received")
            
            # Check sentiment analysis data
            sentiment_data = data.get('sentiment_analysis', [])
            print(f"📄 Sentiment Analysis Items: {len(sentiment_data)}")
            
            # Check related questions
            related_questions = data.get('related_questions', [])
            print(f"❓ Related Questions: {len(related_questions)}")
            
            # Verify language processing
            detected_lang = data.get('processing_language', '')
            if detected_lang.lower() == expected_lang_code or detected_lang == language_name:
                print(f"✅ Language Detection: CORRECT ({detected_lang})")
            else:
                print(f"⚠️ Language Detection: Expected {expected_lang_code}, got {detected_lang}")
            
            return True
            
        else:
            print(f"❌ Status: FAILED ({response.status_code})")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    print("🚀 Testing Multilingual FAISS Integration")
    print("🎯 Single Backend on Port 5010")
    print("=" * 60)
    
    # Test cases for all four languages
    test_cases = [
        {
            "query": "பங்குச் சந்தையின் தற்போதைய நிலை என்ன?",
            "language": "Tamil",
            "code": "ta"
        },
        {
            "query": "స్టాక్ మార్కెట్ ప్రస్తుత స్థితి ఎలా ఉంది?",
            "language": "Telugu", 
            "code": "te"
        },
        {
            "query": "ಷೇರು ಮಾರುಕಟ್ಟೆಯ ಪ್ರಸ್ತುತ ಸ್ಥಿತಿ ಹೇಗಿದೆ?",
            "language": "Kannada",
            "code": "kn"
        },
        {
            "query": "ଷେୟାର ବଜାରର ବର୍ତ୍ତମାନ ଅବସ୍ଥା କେମିତି?",
            "language": "Oriya",
            "code": "or"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*20} Test {i}/4 {'='*20}")
        success = test_language_integration(
            test_case["query"], 
            test_case["language"], 
            test_case["code"]
        )
        results.append((test_case["language"], success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TEST RESULTS")
    print("=" * 60)
    
    for language, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{language:>10}: {status}")
    
    successful_tests = sum(1 for _, success in results if success)
    total_tests = len(results)
    
    print(f"\nOverall Result: {successful_tests}/{total_tests} languages working")
    
    if successful_tests == total_tests:
        print("\n🎉 SUCCESS: All multilingual integration tests passed!")
        print("✅ Single backend (port 5010) successfully handles all languages")
        print("✅ No separate Tamil API needed")
        print("✅ Integrated FAISS processing working for all languages")
    else:
        print(f"\n⚠️ PARTIAL SUCCESS: {successful_tests} out of {total_tests} languages working")
        print("Some languages may need additional debugging")

if __name__ == "__main__":
    main()
