"""
Kannada Query Router Service

This service handles routing Kannada queries directly to the Kannada FAISS API
or falls back to translation-based processing.
"""

import logging
import requests
from typing import Dict, Any, Optional
from services.language_utils import enhanced_language_detector

# Configure logging
logger = logging.getLogger(__name__)

class KannadaQueryRouter:
    """
    Service to route Kannada queries directly to the Kannada FAISS API.
    """
    
    def __init__(self, kannada_api_base_url: str = "http://localhost:5000"):
        """
        Initialize the Kannada query router.
        
        Args:
            kannada_api_base_url: Base URL for the Kannada FAISS API
        """
        self.kannada_api_base_url = kannada_api_base_url.rstrip('/')
        self.kannada_endpoint = f"{self.kannada_api_base_url}/financial_query"
        logger.info(f"Kannada Query Router initialized with endpoint: {self.kannada_endpoint}")
    
    def should_route_to_kannada_api(self, query: str, data_language: Optional[str] = None) -> bool:
        """
        Determine if a query should be routed to the Kannada API.
        For now, we'll return False to use translation flow until Kannada-specific API is available.

        Args:
            query: User query text
            data_language: Language of the stored data (if known)

        Returns:
            bool: False (use translation flow for now)
        """
        # For now, always use translation flow since Kannada-specific API may not be available
        logger.info("Kannada router: Using translation flow (Kannada-specific API not yet available)")
        return False
    
    def route_kannada_query(self, query: str, additional_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Route a Kannada query to the Kannada FAISS API.
        
        Args:
            query: Kannada query text
            additional_params: Additional parameters to pass to the API
            
        Returns:
            Dict[str, Any]: Response from Kannada API or error information
        """
        try:
            # Prepare request payload
            payload = {
                "query": query,
                "language": "Kannada"
            }
            
            # Add any additional parameters
            if additional_params:
                payload.update(additional_params)
            
            logger.info(f"Sending Kannada query to API: {query[:50]}...")
            logger.debug(f"Full payload: {payload}")
            
            # Make request to Kannada API
            response = requests.post(
                self.kannada_endpoint,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("Kannada API request successful")
                
                # Add metadata about routing
                result['routing_info'] = {
                    'routed_to_kannada_api': True,
                    'api_endpoint': self.kannada_endpoint,
                    'processing_type': 'direct_kannada_retrieval'
                }
                
                return result
            else:
                logger.error(f"Kannada API request failed with status {response.status_code}: {response.text}")
                return {
                    'error': f"Kannada API request failed with status {response.status_code}",
                    'error_type': 'kannada_api_error',
                    'routing_info': {
                        'routed_to_kannada_api': True,
                        'api_endpoint': self.kannada_endpoint,
                        'processing_type': 'direct_kannada_retrieval',
                        'error_details': response.text
                    }
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Kannada API request exception: {e}")
            return {
                'error': f"Kannada API connection error: {str(e)}",
                'error_type': 'kannada_api_connection_error',
                'routing_info': {
                    'routed_to_kannada_api': True,
                    'api_endpoint': self.kannada_endpoint,
                    'processing_type': 'direct_kannada_retrieval',
                    'connection_error': str(e)
                }
            }
        except Exception as e:
            logger.error(f"Unexpected error in Kannada routing: {e}")
            return {
                'error': f"Unexpected Kannada routing error: {str(e)}",
                'error_type': 'kannada_routing_error',
                'routing_info': {
                    'routed_to_kannada_api': True,
                    'api_endpoint': self.kannada_endpoint,
                    'processing_type': 'direct_kannada_retrieval',
                    'unexpected_error': str(e)
                }
            }

# Create a global instance for easy import
kannada_router = KannadaQueryRouter()
