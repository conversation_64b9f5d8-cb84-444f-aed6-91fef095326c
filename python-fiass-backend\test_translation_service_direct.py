#!/usr/bin/env python3
"""
Direct test of the translation service to verify the race condition fix
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_translation_service():
    """Test the translation service directly"""
    
    print("🧪 TESTING TRANSLATION SERVICE DIRECTLY")
    print("=" * 60)
    
    try:
        from services.google_translate_api_service_new import google_translate_service
        
        print(f"✅ Service available: {google_translate_service.is_service_available()}")
        print(f"📁 Script path: {google_translate_service.node_script_path}")
        
        # Test data similar to what the main endpoint would send
        test_response_data = {
            'ai_response': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல். அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
            'related_questions': [
                'நிதி சந்தையில் முதலீடு செய்வது எப்படி?',
                'பங்குச் சந்தையில் முதலீடு செய்வது பாதுகாப்பானதா?'
            ],
            'sentence_analysis': [
                {
                    'sentence': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல்.',
                    'sentiment': 'neutral',
                    'confidence': 0.8
                },
                {
                    'sentence': 'அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
                    'sentiment': 'positive', 
                    'confidence': 0.9
                }
            ]
        }
        
        print("\n🔄 Testing translate_financial_response...")
        print(f"📝 Input AI response: {test_response_data['ai_response'][:100]}...")
        
        # Test translation (Tamil to Tamil with reference injection)
        result = google_translate_service.translate_financial_response(
            test_response_data, 
            'Tamil'
        )
        
        print(f"\n📊 RESULTS:")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            data = result.get('data', {})
            ai_response = data.get('ai_response', '')
            print(f"✅ AI Response length: {len(ai_response)}")
            print(f"📝 AI Response preview: {ai_response[:200]}...")
            
            # Check for reference numbers
            if '[1]' in ai_response and '[2]' in ai_response:
                print("✅ Reference numbers found in response!")
            else:
                print("⚠️ Reference numbers not found in response")
                
            related_questions = data.get('related_questions', [])
            print(f"📋 Related questions count: {len(related_questions)}")
            
            if data.get('translation_fallback'):
                print("⚠️ Translation used fallback (rate limited)")
            else:
                print("✅ Translation completed successfully")
                
        else:
            print(f"❌ Translation failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing translation service: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_translation_service()
