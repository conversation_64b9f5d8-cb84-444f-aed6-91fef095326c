#!/usr/bin/env python3
"""
Test script to verify that the regional language truncation issue has been resolved.
This tests Telugu, Kannada, and Oriya responses to ensure they are complete and not truncated.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from full_code import detect_regional_corruption_conservative, detect_text_corruption

def test_regional_corruption_detection():
    """Test that the new conservative corruption detection preserves legitimate content."""
    
    print("🧪 Testing Regional Language Corruption Detection")
    print("=" * 60)
    
    # Test Telugu text with natural repetition (should NOT be flagged as corrupted)
    telugu_text = """
    భారతదేశంలో వ్యవసాయ ఫైనాన్సింగ్ అనేక పథకాలు మరియు సంస్థలను కలిగి ఉంటుంది. ప్రభుత్వం బ్యాంకులు మరియు సహకార సంఘాలు మరియు మైక్రోఫైనాన్స్ సంస్థల ద్వారా వివిధ రుణ సౌకర్యాలను అందిస్తుంది. కిసాన్ క్రెడిట్ కార్డ్ పథకం రైతులకు వారి సాగు మరియు ఇతర అవసరాలకు సకాలంలో రుణ సదుపాయాన్ని అందిస్తుంది.

    1. కిసాన్ క్రెడిట్ కార్డ్ (KCC): ఇది రైతులకు వారి వ్యవసాయ అవసరాలకు మరియు అనుబంధ కార్యకలాపాలకు సకాలంలో మరియు తగిన రుణ సహాయాన్ని అందించడానికి రూపొందించబడింది.

    2. ప్రధాన మంత్రి కిసాన్ సమ్మాన్ నిధి (PM-KISAN): ఈ పథకం కింద, అన్ని భూమి హోల్డర్ రైతు కుటుంబాలకు వ్యవసాయ మరియు అనుబంధ కార్యకలాపాలకు సంబంధించిన ఖర్చులను తీర్చడానికి ఆదాయ మద్దతు అందించబడుతుంది.

    3. పంట బీమా పథకాలు: ప్రధాన మంత్రి ఫసల్ బీమా యోజన (PMFBY) సహజ విపత్తులు, తెగుళ్లు మరియు వ్యాధుల కారణంగా పంట నష్టం జరిగిన సందర్భంలో రైతులకు బీమా కవరేజ్ మరియు ఆర్థిక సహాయాన్ని అందిస్తుంది.
    """
    
    print(f"📝 Testing Telugu text ({len(telugu_text)} characters)")
    is_corrupted, cleaned_text, details = detect_regional_corruption_conservative(telugu_text)
    
    print(f"   Corruption detected: {is_corrupted}")
    print(f"   Original length: {len(telugu_text)}")
    print(f"   Cleaned length: {len(cleaned_text)}")
    print(f"   Content preserved: {len(cleaned_text)/len(telugu_text):.1%}")
    
    if len(cleaned_text) < len(telugu_text) * 0.95:
        print(f"   ❌ ISSUE: Too much content removed!")
    else:
        print(f"   ✅ Good: Content well preserved")
    
    print()
    
    # Test Kannada text
    kannada_text = """
    ಭಾರತದಲ್ಲಿ ಕೃಷಿ ಹಣಕಾಸು ಅನೇಕ ಯೋಜನೆಗಳು ಮತ್ತು ಸಂಸ್ಥೆಗಳನ್ನು ಒಳಗೊಂಡಿದೆ. ಸರ್ಕಾರವು ಬ್ಯಾಂಕುಗಳು ಮತ್ತು ಸಹಕಾರ ಸಂಘಗಳು ಮತ್ತು ಮೈಕ್ರೋಫೈನಾನ್ಸ್ ಸಂಸ್ಥೆಗಳ ಮೂಲಕ ವಿವಿಧ ಸಾಲ ಸೌಲಭ್ಯಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ. ಕಿಸಾನ್ ಕ್ರೆಡಿಟ್ ಕಾರ್ಡ್ ಯೋಜನೆಯು ರೈತರಿಗೆ ಅವರ ಕೃಷಿ ಮತ್ತು ಇತರ ಅಗತ್ಯಗಳಿಗೆ ಸಮಯೋಚಿತ ಸಾಲ ಸೌಲಭ್ಯವನ್ನು ಒದಗಿಸುತ್ತದೆ.
    """
    
    print(f"📝 Testing Kannada text ({len(kannada_text)} characters)")
    is_corrupted, cleaned_text, details = detect_regional_corruption_conservative(kannada_text)
    
    print(f"   Corruption detected: {is_corrupted}")
    print(f"   Original length: {len(kannada_text)}")
    print(f"   Cleaned length: {len(cleaned_text)}")
    print(f"   Content preserved: {len(cleaned_text)/len(kannada_text):.1%}")
    
    if len(cleaned_text) < len(kannada_text) * 0.95:
        print(f"   ❌ ISSUE: Too much content removed!")
    else:
        print(f"   ✅ Good: Content well preserved")
    
    print()
    
    # Test Oriya text
    oriya_text = """
    ଭାରତରେ କୃଷି ଆର୍ଥିକ ଅନେକ ଯୋଜନା ଏବଂ ସଂସ୍ଥା ଅନ୍ତର୍ଭୁକ୍ତ କରେ। ସରକାର ବ୍ୟାଙ୍କ ଏବଂ ସହକାରୀ ସମିତି ଏବଂ ମାଇକ୍ରୋଫାଇନାନ୍ସ ସଂସ୍ଥା ମାଧ୍ୟମରେ ବିଭିନ୍ନ ଋଣ ସୁବିଧା ପ୍ରଦାନ କରନ୍ତି। କିସାନ କ୍ରେଡିଟ କାର୍ଡ ଯୋଜନା କୃଷକମାନଙ୍କୁ ସେମାନଙ୍କର କୃଷି ଏବଂ ଅନ୍ୟାନ୍ୟ ଆବଶ୍ୟକତା ପାଇଁ ସମୟୋଚିତ ଋଣ ସୁବିଧା ପ୍ରଦାନ କରେ।
    """
    
    print(f"📝 Testing Oriya text ({len(oriya_text)} characters)")
    is_corrupted, cleaned_text, details = detect_regional_corruption_conservative(oriya_text)
    
    print(f"   Corruption detected: {is_corrupted}")
    print(f"   Original length: {len(oriya_text)}")
    print(f"   Cleaned length: {len(cleaned_text)}")
    print(f"   Content preserved: {len(cleaned_text)/len(oriya_text):.1%}")
    
    if len(cleaned_text) < len(oriya_text) * 0.95:
        print(f"   ❌ ISSUE: Too much content removed!")
    else:
        print(f"   ✅ Good: Content well preserved")
    
    print()

def test_corruption_patterns():
    """Test that actual corruption patterns are still detected and removed."""
    
    print("🧪 Testing Corruption Pattern Detection")
    print("=" * 60)
    
    # Test text with actual corruption patterns
    corrupted_text = """
    ఇది సాధారణ తెలుగు వచనం __CAPWORD__ మరియు ఇది కూడా [CAPITAL_WORD] కొన్ని సమస్యలు ఉన్నాయి.
    కాపిటల్_వర్డ్_ఫింగ 4 కె 4 ఇది కూడా సమస్య. *.21* ఇది కూడా సమస్య.
    """
    
    print(f"📝 Testing corrupted text ({len(corrupted_text)} characters)")
    is_corrupted, cleaned_text, details = detect_regional_corruption_conservative(corrupted_text)
    
    print(f"   Corruption detected: {is_corrupted}")
    print(f"   Original length: {len(corrupted_text)}")
    print(f"   Cleaned length: {len(cleaned_text)}")
    print(f"   Corruption patterns found: {len(details.get('indicators', []))}")
    
    if is_corrupted:
        print(f"   ✅ Good: Corruption correctly detected")
        print(f"   🔧 Cleaned text preview: {cleaned_text[:100]}...")
    else:
        print(f"   ❌ ISSUE: Corruption not detected!")
    
    print()

def test_integration_with_main_function():
    """Test that the main detect_text_corruption function uses the new conservative approach."""
    
    print("🧪 Testing Integration with Main Function")
    print("=" * 60)
    
    # Test that regional languages use conservative detection
    telugu_sample = "తెలుగు వచనం మరియు మరియు మరియు కొన్ని పదాలు పునరావృతం అవుతాయి"
    
    print(f"📝 Testing main function with Telugu text")
    is_corrupted, cleaned_text, details = detect_text_corruption(telugu_sample)
    
    print(f"   Corruption detected: {is_corrupted}")
    print(f"   Detection method: {details.get('reason', 'unknown')}")
    print(f"   Content preserved: {len(cleaned_text)/len(telugu_sample):.1%}")
    
    if details.get('reason') == 'ultra_conservative_regional_detection':
        print(f"   ✅ Good: Using conservative regional detection")
    else:
        print(f"   ❌ ISSUE: Not using conservative detection!")
    
    print()

if __name__ == "__main__":
    print("🚀 Regional Language Truncation Fix Test")
    print("=" * 60)
    print()
    
    test_regional_corruption_detection()
    test_corruption_patterns()
    test_integration_with_main_function()
    
    print("🎉 Test completed!")
    print()
    print("📋 Summary:")
    print("   - Conservative corruption detection preserves legitimate content")
    print("   - Actual corruption patterns are still detected and removed")
    print("   - All regional languages (Telugu, Kannada, Oriya) use the same approach as Tamil")
    print("   - This should resolve the truncation issues reported by the user")
