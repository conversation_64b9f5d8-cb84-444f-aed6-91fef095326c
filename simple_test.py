#!/usr/bin/env python3
"""
Simple test to check if the backend is responding
"""

import requests
import json

def test_health():
    """Test the health endpoint"""
    try:
        print("Testing health endpoint...")
        response = requests.get("http://localhost:5010/api/health", timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health test failed: {e}")
        return False

def test_tamil_query():
    """Test a simple Tamil query"""
    try:
        print("\nTesting Tamil query...")
        query = "பங்குச் சந்தை என்ன?"
        
        response = requests.post(
            "http://localhost:5010/api/multilingual_financial_query",
            json={"query": query},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success', False)}")
            print(f"Processing language: {data.get('processing_language', 'Unknown')}")
            if data.get('ai_response'):
                print(f"Response preview: {data['ai_response'][:100]}...")
            else:
                print("No AI response received")
        else:
            print(f"Error response: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Tamil query test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple Backend Test")
    print("=" * 30)
    
    health_ok = test_health()
    tamil_ok = test_tamil_query()
    
    print("\n" + "=" * 30)
    print("Results:")
    print(f"Health endpoint: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"Tamil query: {'✅ PASS' if tamil_ok else '❌ FAIL'}")
