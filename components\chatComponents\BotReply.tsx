import React, { useEffect, useState, useRef } from "react";
import Image from "next/image";
import logo from "@/public/images/favicon.ico";
import {
  PiArrowsCounterClockwise,
  PiCopy,
  PiShareFat,
  PiSpeakerHigh,
  PiThumbsDown,
  PiThumbsUp,
  PiCaretUpDown
} from "react-icons/pi";
import ReactMarkdown from 'react-markdown';
import LoadingIndicator from "./LoadingIndicator";
import PerplexityStyleResponse from "./PerplexityStyleResponse";
import ClientOnly from "@/components/ui/ClientOnly";

// Define the shape of complex AI responses
export interface ComplexAIResponse {
  summary?: string;
  isCommandSuggestion?: boolean;
  commands?: { command: string; label: string }[];
  code?: string;
  language?: string;
  image?: string;
  ai_response?: string;
  text?: string;
  related_questions?: string[];
  sentence_analysis?: Array<{
    sentence: string;
    url: string;
    summary?: string;
    source_title?: string;
    source_type?: string;
    file_id?: string;
    file_name?: string;
    page?: string;
    page_content?: string;
    vector_id?: string;
    file_uploaded?: string;
  }>;
}

// Note: We're using ComplexAIResponse for type checking instead of a separate ResponseObject interface

type BotReplyProps = {
  replyType: string;
  setScroll: React.Dispatch<React.SetStateAction<boolean>>;
  isAnimation: boolean;
  aiResponse: any; // Changed from string | ComplexAIResponse | null to any
  timestamp?: string;
  messageId?: string; // No longer used for animation, kept for compatibility
  selectedLanguage?: string; // Added for language-specific text-to-speech
  onSelectQuestion?: (question: string) => void; // Added for related questions
  isSharedView?: boolean; // Added for shared chat view
};

const BotReply: React.FC<BotReplyProps> = ({
  replyType, // Used to determine the type of reply (response, loading, etc.)
  setScroll,
  isAnimation,
  aiResponse,
  timestamp,
  messageId,
  selectedLanguage = "English", // Default to English if not provided
  onSelectQuestion,
  isSharedView = false // Default to false for normal chat view
}) => {
  const [copied, setCopied] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [feedback, setFeedback] = useState<'like' | 'dislike' | null>(null);
  const speechSynthesisRef = useRef<SpeechSynthesisUtterance | null>(null);
  const [showIndexes, setShowIndexes] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Language detection functions
  const isTamilText = (text: string): boolean => {
    // Tamil Unicode range: \u0B80-\u0BFF
    const tamilRegex = /[\u0B80-\u0BFF]/;
    return tamilRegex.test(text);
  };

  const isTeluguText = (text: string): boolean => {
    // Telugu Unicode range: \u0C00-\u0C7F
    const teluguRegex = /[\u0C00-\u0C7F]/;
    return teluguRegex.test(text);
  };

  // Language code mapping
  const getLanguageCode = (language: string): string => {
    const languageMap: Record<string, string> = {
      "English": "en-US",
      "Tamil": "ta-IN",
      "Telugu": "te-IN"
    };
    return languageMap[language] || "en-US"; // Default to English if not found
  };

  // Function to render sentence analysis with URLs
  const renderSentenceAnalysis = () => {
    // We're now using the PerplexityStyleResponse component to display references
    // This function is kept for backward compatibility but doesn't render anything
    return null;
  };

  // Immediately set scroll when component mounts
  useEffect(() => {
    setScroll(true);
  }, [setScroll]);

  // Handle click outside to close the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowIndexes(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize speech synthesis voices
  useEffect(() => {
    // Some browsers need an explicit call to load voices
    if (window.speechSynthesis) {
      // Load voices if available
      const loadVoices = () => {
        const voices = window.speechSynthesis.getVoices();
        if (voices.length > 0) {
          console.log(`Loaded ${voices.length} speech synthesis voices`);

          // Log available voices for debugging
          voices.forEach(voice => {
            console.log(`Voice: ${voice.name}, Lang: ${voice.lang}, Default: ${voice.default}`);
          });

          // Check if we have Tamil or Telugu voices
          const tamilVoice = voices.find(v => v.lang.includes('ta') || v.name.toLowerCase().includes('tamil'));
          const teluguVoice = voices.find(v => v.lang.includes('te') || v.name.toLowerCase().includes('telugu'));

          console.log(`Tamil voice available: ${tamilVoice ? 'Yes - ' + tamilVoice.name : 'No'}`);
          console.log(`Telugu voice available: ${teluguVoice ? 'Yes - ' + teluguVoice.name : 'No'}`);
        } else {
          console.warn("No speech synthesis voices loaded");
        }
      };

      // Try to load voices immediately
      loadVoices();

      // Also set up an event listener for when voices change/load
      window.speechSynthesis.onvoiceschanged = loadVoices;

      // Cleanup
      return () => {
        window.speechSynthesis.onvoiceschanged = null;
      };
    }
  }, []);

  // Cleanup effect to stop speech synthesis when component unmounts
  useEffect(() => {
    return () => {
      // Always try to cancel speech synthesis on unmount, regardless of isSpeaking state
      // This ensures we clean up properly even if state gets out of sync
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();

        // Also clear the reference
        if (speechSynthesisRef.current) {
          speechSynthesisRef.current = null;
        }
      }
    };
  }, []);

  // Added useEffect to ensure logging happens after render
  useEffect(() => {
    console.log("BotReply received aiResponse:", aiResponse);
    if (aiResponse === undefined) {
      console.warn("Warning: BotReply received undefined aiResponse");
    } else if (aiResponse === null) {
      console.warn("Warning: BotReply received null aiResponse");
    } else {
      console.log("BotReply aiResponse type:", typeof aiResponse);
      if (typeof aiResponse === 'string') {
        console.log("BotReply aiResponse string length:", aiResponse.length);
        console.log("BotReply aiResponse string sample:", aiResponse.substring(0, 50) + (aiResponse.length > 50 ? '...' : ''));

        // Try to parse if it looks like JSON
        if (aiResponse.trim().startsWith('{') && aiResponse.trim().endsWith('}')) {
          try {
            const parsed = JSON.parse(aiResponse);
            console.log("BotReply successfully parsed aiResponse as JSON:", parsed);
          } catch (e) {
            console.log("BotReply aiResponse is not valid JSON");
          }
        }
      } else if (typeof aiResponse === 'object') {
        console.log("BotReply aiResponse keys:", Object.keys(aiResponse || {}));

        // Check for common properties
        if (aiResponse && 'ai_response' in aiResponse) {
          console.log("BotReply found ai_response property:",
            typeof aiResponse.ai_response === 'string'
              ? aiResponse.ai_response.substring(0, 50) + '...'
              : aiResponse.ai_response);
        }
      }
    }
  }, [aiResponse]);

  // Add a direct check for aiResponse to provide a fallback
  const safeAiResponse = aiResponse === undefined || aiResponse === null
    ? "Sorry, there was an issue with this response."
    : typeof aiResponse === 'string'
      ? aiResponse
      : typeof aiResponse === 'object' && aiResponse.ai_response
        ? aiResponse.ai_response
        : JSON.stringify(aiResponse, null, 2);

  // Log the related questions if they exist
  if (typeof aiResponse === 'object' && aiResponse !== null && Array.isArray(aiResponse.related_questions)) {
    console.log("BotReply: Found related_questions in aiResponse:", aiResponse.related_questions);
  }

  const [formattedTime, setFormattedTime] = useState('just now');

  // Format time on client side to prevent hydration errors
  useEffect(() => {
    if (timestamp && typeof window !== 'undefined') {
      const time = new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      setFormattedTime(time);
    }
  }, [timestamp]);

  // Process the response to get the content to display
  const getResponseContent = () => {
    // Log the raw response for debugging
    console.log("getResponseContent processing aiResponse:", safeAiResponse);

    // If it's null or undefined (should not happen with safeAiResponse)
    if (safeAiResponse === null || safeAiResponse === undefined) {
      console.log("safeAiResponse is null/undefined");
      return 'Sorry, there was an issue with this response.';
    }

    // If it's a string (which is the expected format from the API)
    if (typeof safeAiResponse === 'string') {
      // Just return the string directly - this is the AI response text
      console.log("safeAiResponse is a string of length:", safeAiResponse.length);

      // Check if it's a JSON string that needs parsing
      if (safeAiResponse.trim().startsWith('{') && safeAiResponse.trim().endsWith('}')) {
        try {
          const parsed = JSON.parse(safeAiResponse);
          console.log("Successfully parsed JSON string:", parsed);

          // If the parsed object has an ai_response property, extract it
          if (parsed.ai_response) {
            console.log("Found ai_response in parsed JSON, using that");
            return typeof parsed.ai_response === 'string'
              ? parsed.ai_response
              : JSON.stringify(parsed.ai_response);
          }

          // If it has a text property
          if (parsed.text) {
            console.log("Found text property in parsed JSON");
            return typeof parsed.text === 'string'
              ? parsed.text
              : JSON.stringify(parsed.text);
          }

          // Return the parsed object as a string
          return JSON.stringify(parsed);
        } catch (e) {
          console.log("Not valid JSON, keeping as string");
        }
      }

      // Enhanced cleaning for multilingual text with better Unicode support and bullet point formatting
      // NOTE: Capital word preservation is handled by the backend translation service
      // Frontend should NOT create any placeholders - backend already handles this correctly
      let cleanedResponse = safeAiResponse;
      
      // Check for any remaining problematic placeholders that shouldn't be there
      const problematicPlaceholders = cleanedResponse.match(/__capital_word_\w+__/gi);
      if (problematicPlaceholders) {
        console.warn("BotReply: Found problematic placeholders that should have been handled by backend:", problematicPlaceholders);
        // Try to extract the original word from the placeholder pattern
        cleanedResponse = cleanedResponse.replace(/__capital_word_(\w+)__/gi, (match, word) => {
          // Try to reconstruct the original capital word
          return word.toUpperCase();
        });
      }
      
      // Check for old numeric placeholders that might not have been restored
      const numericPlaceholders = cleanedResponse.match(/999\d+999/g);
      if (numericPlaceholders) {
        console.warn("BotReply: Found old numeric placeholders that should have been restored by backend:", numericPlaceholders);
        // Replace with generic placeholder since we can't restore without mapping
        cleanedResponse = cleanedResponse.replace(/999\d+999/g, '[CAPITAL_WORD]');
      }
      
      // Check for new CAPWORD placeholders that might not have been restored
      const capwordPlaceholders = cleanedResponse.match(/CAPWORD\d+_[a-f0-9]+_CAPWORD/g);
      if (capwordPlaceholders) {
        console.warn("BotReply: Found CAPWORD placeholders that should have been restored by backend:", capwordPlaceholders);
        // Replace with generic placeholder since we can't restore without mapping
        cleanedResponse = cleanedResponse.replace(/CAPWORD\d+_[a-f0-9]+_CAPWORD/g, '[CAPITAL_WORD]');
      }
      
      // Check for any other suspicious placeholder patterns
      const otherPlaceholders = cleanedResponse.match(/\d+[A-Z]+\d+/g);
      if (otherPlaceholders) {
        console.warn("BotReply: Found suspicious placeholder patterns:", otherPlaceholders);
        // These might be corrupted placeholders, replace them
        cleanedResponse = cleanedResponse.replace(/\d+[A-Z]+\d+/g, '[CAPITAL_WORD]');
      }
      
      // Fix repeated words and broken sentences with improved multilingual support
      cleanedResponse = cleanedResponse
        // Remove excessive repetition of Tamil words (improved pattern)
        .replace(/([\u0B80-\u0BFF]+(?:\s+[\u0B80-\u0BFF]+)*)(\s+\1){2,}/g, '$1')
        // Remove excessive repetition of Telugu words
        .replace(/([\u0C00-\u0C7F]+(?:\s+[\u0C00-\u0C7F]+)*)(\s+\1){2,}/g, '$1')
        // Remove excessive repetition of Kannada words
        .replace(/([\u0C80-\u0CFF]+(?:\s+[\u0C80-\u0CFF]+)*)(\s+\1){2,}/g, '$1')
        // Remove excessive repetition of English words (more conservative)
        .replace(/(\b[A-Za-z]{3,}\b)(\s+\1){2,}/g, '$1')
        // Fix broken sentence patterns with multilingual punctuation
        .replace(/([.!?।॥])\s*([.!?।॥])+/g, '$1')
        // Clean up multiple spaces but preserve intentional spacing for lists
        .replace(/[ \t]{4,}/g, '  ')
        // Fix broken punctuation spacing for multilingual text
        .replace(/([.!?।॥])\s*([^\s\n.!?।॥])/g, '$1 $2')
        // Fix broken line breaks that cut off sentences (more careful with Unicode)
        .replace(/([^\s.!?।॥\n])\s*\n\s*([^\s\n])/g, '$1 $2')
        // Clean up trailing incomplete fragments (preserve Unicode completeness)
        .replace(/\s+[^\s.!?।॥\n\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF]*\s*$/, '')
        // Enhanced bullet point and list formatting
        // Convert various bullet point formats to standard markdown
        .replace(/^[\s]*[•·▪▫‣⁃]\s*/gm, '- ')
        .replace(/^[\s]*[*]\s+/gm, '- ')
        .replace(/^[\s]*[-]\s*/gm, '- ')
        // Fix numbered lists with proper spacing
        .replace(/^[\s]*(\d+)[\.\)]\s*/gm, '$1. ')
        // Fix broken list items that might be missing content
        .replace(/^([-*]\s*)$/gm, '')
        .replace(/^(\d+\.\s*)$/gm, '')
        // Ensure proper line breaks before headers
        .replace(/([^\n])\n(#{1,6}\s)/g, '$1\n\n$2')
        // Ensure proper line breaks after headers
        .replace(/(#{1,6}[^\n]*)\n([^\n#])/g, '$1\n\n$2')
        // Fix paragraph spacing - ensure double line breaks between paragraphs
        .replace(/([.!?।॥])\s*\n\s*([^\s\n-#\d])/g, '$1\n\n$2')
        // Ensure proper spacing around lists
        .replace(/([^\n])\n([-*]\s)/g, '$1\n\n$2')
        .replace(/([^\n])\n(\d+\.\s)/g, '$1\n\n$2')
        // Fix incomplete sentences or fragments at line endings
        .replace(/([^\s.!?।॥])\s*\n\s*([.!?।॥])/g, '$1$2')
        // Clean up excessive line breaks but preserve intentional spacing
        .replace(/\n{4,}/g, '\n\n\n')
        // Fix broken multilingual text patterns
        .replace(/([\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF]+)\s*\n\s*([\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF]+)/g, '$1 $2')
        // Normalize Unicode to ensure proper rendering
        .normalize('NFC')
        .trim();
      
      console.log("BotReply: Text cleaning completed (no placeholder handling needed - backend handles capital word preservation)");

      // Log the cleaning process for debugging
      if (safeAiResponse !== cleanedResponse) {
        console.log("Text cleaning applied:");
        console.log("Original length:", safeAiResponse.length);
        console.log("Cleaned length:", cleanedResponse.length);
        console.log("Original sample:", safeAiResponse.substring(0, 100) + "...");
        console.log("Cleaned sample:", cleanedResponse.substring(0, 100) + "...");
      }

      return cleanedResponse;
    }

    // If somehow we got an object instead of a string
    if (typeof safeAiResponse === 'object') {
      console.log("safeAiResponse is an object with keys:", Object.keys(safeAiResponse || {}));

      // Cast to ResponseObject to access properties safely
      const responseObj = safeAiResponse as ComplexAIResponse;

      // If it has an ai_response property (maybe the whole API response was passed)
      if (responseObj && 'ai_response' in responseObj) {
        console.log("Found ai_response property in object");

        // Check for related_questions
        if ('related_questions' in responseObj && Array.isArray(responseObj.related_questions)) {
          console.log("Found related_questions in object:", responseObj.related_questions);
        }

        const response = responseObj.ai_response;
        const responseText = typeof response === 'string' ? response : JSON.stringify(response);
        
        // Apply the same cleaning to object responses
        if (typeof responseText === 'string') {
          return responseText
            .replace(/(\b\w+\b)(\s+\1){2,}/g, '$1')
            .replace(/([.!?])\s*([.!?])+/g, '$1')
            .replace(/\s{2,}/g, ' ')
            .replace(/([.!?])\s*([^\s])/g, '$1 $2')
            .replace(/\s+[^\s.!?]*$/, '')
            .trim();
        }
        
        return responseText;
      }

      // If it has a summary property
      if (responseObj && 'summary' in responseObj) {
        console.log("Found summary property in object");
        const summary = responseObj.summary;
        return typeof summary === 'string' ? summary : JSON.stringify(summary);
      }

      // If it has a text property (from message object)
      if (responseObj && 'text' in responseObj) {
        console.log("Found text property in object");
        const text = responseObj.text;
        return typeof text === 'string' ? text : JSON.stringify(text);
      }

      // Check for sentence_analysis (just for logging, we'll handle display separately)
      if (responseObj && 'sentence_analysis' in responseObj) {
        console.log("Found sentence_analysis in object:", responseObj.sentence_analysis);
      }

      // Last resort, stringify the object
      console.log("No recognized properties, stringifying object");
      try {
        return JSON.stringify(safeAiResponse, null, 2);
      } catch (e) {
        console.error("Failed to stringify safeAiResponse:", e);
        return "Error: Could not process this response.";
      }
    }

    // Fallback for any other type
    console.log("safeAiResponse is an unexpected type:", typeof safeAiResponse);
    try {
      return String(safeAiResponse);
    } catch (e) {
      console.error("Failed to convert safeAiResponse to string:", e);
      return "Error: Could not convert response to string.";
    }
  };

  // Get the response content and ensure it's not empty
  const responseContent = getResponseContent();
  console.log("BotReply: responseContent after processing:",
    typeof responseContent === 'string'
      ? `${responseContent.substring(0, 50)}... (length: ${responseContent.length})`
      : responseContent);

  // Get API environment and Pinecone indexes if available
  const apiEnvironment = typeof aiResponse === 'object' && aiResponse !== null
    ? aiResponse.api_environment || 'production'
    : 'production';

  const pineconeIndexes = typeof aiResponse === 'object' && aiResponse !== null && Array.isArray(aiResponse.pinecone_indexes)
    ? aiResponse.pinecone_indexes
    : [];

  // Handle index selection
  const handleIndexSelect = (index: string) => {
    setSelectedIndex(index);
    // You could add functionality here to use the selected index
    console.log(`Selected index: ${index}`);
  };

  // Process response for special features if it's an object
  const getComplexResponse = () => {
    if (!aiResponse) return null;

    // If it's already an object with our expected structure
    if (typeof aiResponse === 'object') {
      // Check for related_questions at the top level
      if (Array.isArray(aiResponse.related_questions) && aiResponse.related_questions.length > 0) {
        console.log("Found related_questions at top level of object:", aiResponse.related_questions);
      }

      // Check for related_questions in the text property
      if (typeof aiResponse.text === 'object' && aiResponse.text !== null &&
          Array.isArray(aiResponse.text.related_questions) && aiResponse.text.related_questions.length > 0) {
        console.log("Found related_questions in text property:", aiResponse.text.related_questions);
      }

      return aiResponse;
    }

    // If it's a string that might be a stringified object
    if (typeof aiResponse === 'string') {
      try {
        const parsed = JSON.parse(aiResponse);
        // Check if it has any of our complex properties
        if (parsed.code || parsed.commands || parsed.isCommandSuggestion ||
            (Array.isArray(parsed.sentence_analysis) && parsed.sentence_analysis.length > 0) ||
            (Array.isArray(parsed.related_questions) && parsed.related_questions.length > 0)) {
          console.log("Found complex response with properties:", Object.keys(parsed));

          // Log related questions if they exist
          if (Array.isArray(parsed.related_questions) && parsed.related_questions.length > 0) {
            console.log("Found related_questions in parsed JSON:", parsed.related_questions);
          }

          return parsed;
        }
      } catch (e) {
        // Not JSON, just a regular string
        return null;
      }
    }

    return null;
  };

  const complexResponse = getComplexResponse();

  // Handle text-to-speech
  const handleTextToSpeech = () => {
    // Check if speech synthesis is available
    if (!window.speechSynthesis) {
      console.error('Speech synthesis not supported in this browser');
      alert('Text-to-speech is not supported in your browser.');
      return;
    }

    // If already speaking, stop it
    if (isSpeaking) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      return;
    }

    // Get the text to speak
    let textToSpeak = responseContent;

    // Check if we have valid text to speak
    if (!textToSpeak || textToSpeak.trim() === '') {
      console.error('No valid text to speak');
      alert('No text available for speech synthesis.');
      return;
    }

    // Clean up the text if it contains markdown or code
    // This is a simple cleanup - you might want to enhance this
    textToSpeak = textToSpeak.replace(/```[\s\S]*?```/g, 'Code block omitted for speech.');
    textToSpeak = textToSpeak.replace(/`([^`]+)`/g, '$1');
    textToSpeak = textToSpeak.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
    textToSpeak = textToSpeak.replace(/#+\s+/g, '');

    // Determine the language to use for speech
    let detectedLanguage = selectedLanguage;

    // Auto-detect language from text if needed
    const hasTamilChars = isTamilText(textToSpeak);
    const hasTeluguChars = isTeluguText(textToSpeak);

    if (hasTamilChars) {
      detectedLanguage = "Tamil";
      console.log("Detected Tamil text for speech synthesis");
    } else if (hasTeluguChars) {
      detectedLanguage = "Telugu";
      console.log("Detected Telugu text for speech synthesis");
    }

    // Get the language code for speech synthesis
    const languageCode = getLanguageCode(detectedLanguage);
    console.log(`Using language code for speech synthesis: ${languageCode}`);

    // Create a new SpeechSynthesisUtterance instance
    const utterance = new SpeechSynthesisUtterance(textToSpeak);

    // Set language
    utterance.lang = languageCode;

    // Special handling for Tamil and Telugu text
    if (hasTamilChars || hasTeluguChars) {
      // Log the text being spoken for debugging
      console.log(`Speaking ${detectedLanguage} text: ${textToSpeak.substring(0, 100)}${textToSpeak.length > 100 ? '...' : ''}`);

      // Count the number of characters in the text
      const tamilCharCount = hasTamilChars ?
        textToSpeak.split('').filter((char: string) => /[\u0B80-\u0BFF]/.test(char)).length : 0;

      const teluguCharCount = hasTeluguChars ?
        textToSpeak.split('').filter((char: string) => /[\u0C00-\u0C7F]/.test(char)).length : 0;

      console.log(`${detectedLanguage} character count: ${tamilCharCount || teluguCharCount}`);

      // If the text has a mix of Tamil/Telugu and other characters, we might need special handling
      if ((tamilCharCount > 0 && tamilCharCount < textToSpeak.length / 2) ||
          (teluguCharCount > 0 && teluguCharCount < textToSpeak.length / 2)) {
        console.log(`Mixed text detected with ${detectedLanguage} characters`);
      }
    }

    // Set properties
    utterance.rate = 1.0; // Speech rate (0.1 to 10)
    utterance.pitch = 1.0; // Speech pitch (0 to 2)
    utterance.volume = 1.0; // Speech volume (0 to 1)

    // Try to find an appropriate voice for the selected language
    const voices = window.speechSynthesis.getVoices();
    console.log(`Available voices for selection: ${voices.length}`);

    // Enhanced voice selection logic
    let selectedVoice = null;

    // First try: exact language code match
    selectedVoice = voices.find(v => v.lang === languageCode);

    // Second try: language code prefix match (e.g., 'ta' for Tamil)
    if (!selectedVoice) {
      const langPrefix = languageCode.split('-')[0];
      selectedVoice = voices.find(v => v.lang.startsWith(langPrefix));

      // For Tamil and Telugu, try additional matching patterns
      if (!selectedVoice) {
        if (detectedLanguage === "Tamil") {
          // Try to find any voice with 'tamil' in the name or 'ta' in the language code
          selectedVoice = voices.find(v =>
            v.name.toLowerCase().includes('tamil') ||
            v.lang.includes('ta')
          );
        } else if (detectedLanguage === "Telugu") {
          // Try to find any voice with 'telugu' in the name or 'te' in the language code
          selectedVoice = voices.find(v =>
            v.name.toLowerCase().includes('telugu') ||
            v.lang.includes('te')
          );
        }
      }
    }

    // Third try: find any Indian voice for Indian languages
    if (!selectedVoice && (detectedLanguage === "Tamil" || detectedLanguage === "Telugu")) {
      selectedVoice = voices.find(v =>
        v.lang.includes('in-') || // Indian locale
        v.name.toLowerCase().includes('india') ||
        v.name.toLowerCase().includes('indian')
      );
    }

    // If we found a voice, use it
    if (selectedVoice) {
      console.log(`Selected voice: ${selectedVoice.name} (${selectedVoice.lang}) for ${detectedLanguage}`);
      utterance.voice = selectedVoice;
    } else {
      console.warn(`No matching voice found for ${languageCode} (${detectedLanguage}), using default voice`);

      // As a last resort for Tamil/Telugu, try to use a voice that can handle Unicode properly
      if (detectedLanguage === "Tamil" || detectedLanguage === "Telugu") {
        // Try to find a Google voice as they tend to handle Unicode better
        const googleVoice = voices.find(v => v.name.includes('Google'));
        if (googleVoice) {
          console.log(`Using Google voice as fallback: ${googleVoice.name}`);
          utterance.voice = googleVoice;
        }
      }
    }

    // Set event handlers
    utterance.onstart = () => {
      setIsSpeaking(true);
    };

    utterance.onend = () => {
      setIsSpeaking(false);
      speechSynthesisRef.current = null;
    };

    utterance.onerror = (event) => {
      // Extract more specific error information if available
      const errorMessage = event.error || 'Unknown speech synthesis error';
      const errorDetails = {
        message: errorMessage,
        timeStamp: event.timeStamp,
        type: event.type,
        bubbles: event.bubbles,
        cancelable: event.cancelable
      };

      console.error('Speech synthesis error:', errorDetails);

      // Reset state
      setIsSpeaking(false);
      speechSynthesisRef.current = null;

      // Optionally show a user-friendly error message
      // You could add a toast notification or other UI feedback here
    };

    // Store the utterance in the ref for later cancellation
    speechSynthesisRef.current = utterance;

    // Start speaking with error handling
    try {
      // Make sure any previous speech is cancelled
      window.speechSynthesis.cancel();

      // Special handling for Tamil and Telugu text in browsers that might not support it well
      if ((detectedLanguage === "Tamil" || detectedLanguage === "Telugu") &&
          (hasTamilChars || hasTeluguChars)) {

        // Some browsers might have issues with Tamil/Telugu text
        // Let's add some additional logging and handling
        console.log(`Using special handling for ${detectedLanguage} text-to-speech`);

        // Ensure the speech synthesis service is ready
        window.speechSynthesis.cancel();

        // Force a small delay before speaking to ensure the speech synthesis is ready
        setTimeout(() => {
          try {
            // Start the speech
            window.speechSynthesis.speak(utterance);
            console.log(`Started speech synthesis for ${detectedLanguage} text`);

            // Set speaking state
            setIsSpeaking(true);
          } catch (innerError) {
            console.error(`Error in delayed speech synthesis for ${detectedLanguage}:`, innerError);
            setIsSpeaking(false);
            speechSynthesisRef.current = null;
          }
        }, 100);
      } else {
        // Standard handling for other languages
        // Start the new speech
        window.speechSynthesis.speak(utterance);

        // Some browsers might not trigger the onstart event properly
        // Set a fallback timeout to check if speaking actually started
        setTimeout(() => {
          if (speechSynthesisRef.current === utterance && !isSpeaking) {
            console.warn('Speech synthesis may not have started properly');
            setIsSpeaking(true); // Force update state if onstart didn't fire
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Exception during speech synthesis initialization:', error);
      setIsSpeaking(false);
      speechSynthesisRef.current = null;
      alert('Failed to start text-to-speech. Please try again.');
    }
  };

  // Handle feedback (like/dislike)
  const handleFeedback = (type: 'like' | 'dislike') => {
    // Toggle feedback if already selected
    if (feedback === type) {
      setFeedback(null);
    } else {
      setFeedback(type);
    }

    // Here you would typically send the feedback to your backend
    // This is a placeholder for the actual implementation
    console.log(`User ${feedback === type ? 'removed' : 'gave'} ${type} feedback for message ID: ${messageId}`);

    // Example of how you might send this to an API
    // if (messageId) {
    //   fetch('/api/feedback', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify({
    //       messageId,
    //       feedback: feedback === type ? null : type,
    //       timestamp: new Date().toISOString()
    //     })
    //   });
    // }
  };

  // Regenerate functionality is currently disabled in the UI

  // Handle sharing
  const handleShare = () => {
    // Check if Web Share API is available
    if (navigator.share) {
      navigator.share({
        title: 'AIQuill Response',
        text: responseContent,
        // url: window.location.href, // Uncomment if you want to share the URL
      })
      .then(() => console.log('Shared successfully'))
      .catch((error) => console.error('Error sharing:', error));
    } else {
      // Fallback for browsers that don't support the Web Share API
      handleCopy();
      alert('Link copied to clipboard! The Web Share API is not supported in this browser.');
    }
  };

  // Handle copy to clipboard
  const handleCopy = () => {
    if (responseContent) {
      navigator.clipboard.writeText(responseContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Fallback if no content
  if ((!responseContent || (typeof responseContent === 'string' && responseContent.trim() === '')) && !isAnimation) {
    console.warn("BotReply: No content to display", {
      aiResponseType: typeof aiResponse,
      safeAiResponseType: typeof safeAiResponse,
      responseContentType: typeof responseContent,
      aiResponseValue: aiResponse,
      safeAiResponseValue: safeAiResponse
    });

    // Try to display the raw safeAiResponse directly
    let directDisplay = null;

    if (typeof safeAiResponse === 'string') {
      directDisplay = safeAiResponse;
    } else if (typeof safeAiResponse === 'object' && safeAiResponse !== null) {
      // Cast to ComplexAIResponse to access properties safely
      const responseObj = safeAiResponse as ComplexAIResponse;

      // Try to extract text from various possible properties
      if ('ai_response' in responseObj) {
        directDisplay = responseObj.ai_response;
      } else if ('text' in responseObj) {
        directDisplay = responseObj.text;
      } else if ('summary' in responseObj) {
        directDisplay = responseObj.summary;
      } else {
        // Last resort, stringify the object
        try {
          directDisplay = JSON.stringify(safeAiResponse, null, 2);
        } catch (e) {
          directDisplay = "Error: Could not stringify response object.";
        }
      }
    } else {
      // For any other type, convert to string
      directDisplay = String(safeAiResponse);
    }

    return (
      <div className="flex justify-start items-start gap-1 sm:gap-3 w-full max-w-[90%]">
        <Image src={logo} alt="AIQuill logo" className="max-sm:size-5 object-cover" width={32} height={32} />
        <div className="flex flex-col justify-start items-start gap-3 flex-1">
          <div className="flex justify-between items-center w-full">
            <p className="text-xs text-n100">QueryOne, {formattedTime}</p>

            {/* API Environment and Pinecone Indexes Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <div
                className={`text-xs mb-2 flex items-center gap-1 cursor-pointer ${apiEnvironment === 'development' ? 'text-blue-500' : 'text-gray-400'}`}
                onClick={() => apiEnvironment === 'development' && pineconeIndexes.length > 0 && setShowIndexes(!showIndexes)}
              >
                <span>API: {apiEnvironment === 'development' ? 'Development' : 'Production'}</span>
                {apiEnvironment === 'development' && pineconeIndexes.length > 0 && (
                  <PiCaretUpDown className="inline-block" />
                )}
              </div>

              {/* Pinecone Indexes Dropdown */}
              {showIndexes && apiEnvironment === 'development' && pineconeIndexes.length > 0 && (
                <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 min-w-[200px]">
                  <div className="py-1 px-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                    Fiass Indexes
                  </div>
                  <div className="max-h-[200px] overflow-y-auto">
                    {pineconeIndexes.map((index: string, i: number) => (
                      <div
                        key={i}
                        className={`py-2 px-3 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}
                        onClick={() => handleIndexSelect(index)}
                      >
                        {index}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          <div 
            className="text-sm bg-primaryColor/5 py-4 px-6 border border-primaryColor/20 rounded-lg w-full sm:max-w-[90%] shadow-sm"
            style={{
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Noto Sans Tamil", "Noto Sans Telugu", "Noto Sans Kannada", "Noto Sans Oriya", "Helvetica Neue", Arial, sans-serif',
              lineHeight: '1.7',
              letterSpacing: '0.01em',
              textRendering: 'optimizeLegibility',
              WebkitFontSmoothing: 'antialiased',
              MozOsxFontSmoothing: 'grayscale'
            }}
          >
            {directDisplay ? (
              <div className="whitespace-pre-wrap">
                {typeof aiResponse === 'object' && aiResponse !== null &&
                (Array.isArray(aiResponse.sentence_analysis) || Array.isArray(aiResponse.related_questions)) ? (
                  <ClientOnly fallback={<ReactMarkdown>{directDisplay}</ReactMarkdown>}>
                    <PerplexityStyleResponse
                      text={directDisplay}
                      sentenceAnalysis={aiResponse.sentence_analysis || []}
                      relatedQuestions={aiResponse.related_questions || []}
                      onSelectQuestion={(question) => {
                        console.log("BotReply: Selected question:", question);
                        if (onSelectQuestion) {
                          onSelectQuestion(question);
                          // The ChatBox component will handle the automatic send
                          // No need to manually trigger Enter here
                        }
                      }}
                      selectedLanguage={selectedLanguage}
                    />
                  </ClientOnly>
                ) : typeof aiResponse === 'object' && aiResponse !== null &&
                   typeof aiResponse.text === 'object' && aiResponse.text !== null &&
                   Array.isArray(aiResponse.text.related_questions) ? (
                  <ClientOnly fallback={<ReactMarkdown>{typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : directDisplay}</ReactMarkdown>}>
                    <PerplexityStyleResponse
                      text={typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : directDisplay}
                      sentenceAnalysis={aiResponse.text.sentence_analysis || []}
                      relatedQuestions={aiResponse.text.related_questions || []}
                      onSelectQuestion={(question) => {
                        console.log("BotReply: Selected question:", question);
                        if (onSelectQuestion) {
                          onSelectQuestion(question);
                          // The ChatBox component will handle the automatic send
                          // No need to manually trigger Enter here
                        }
                      }}
                      selectedLanguage={selectedLanguage}
                    />
                  </ClientOnly>
                ) : (
                  <ReactMarkdown>{directDisplay}</ReactMarkdown>
                )}
              </div>
            ) : (
              <>
                <p>I'm sorry, there was an issue processing this response.</p>
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-2 text-xs text-gray-500">
                    <p>Debug info:</p>
                    <p>aiResponse type: {typeof aiResponse}</p>
                    <p>aiResponse value: {aiResponse ? (typeof aiResponse === 'string' ? aiResponse.substring(0, 100) + '...' : JSON.stringify(aiResponse)) : 'null'}</p>
                  </div>
                )}
                {renderSentenceAnalysis()}
              </>
            )}
          </div>
          {!isSharedView && (
            <div className="flex justify-end items-center gap-2">
            {/* <button
                className={`p-1 hover:bg-gray-100 rounded-full ${isSpeaking ? 'bg-blue-100 text-blue-600' : ''}`}
                title={isSpeaking ? "Stop text to speech" : "Text to speech"}
                onClick={handleTextToSpeech}
              >
                <PiSpeakerHigh />
              </button> */}
              <button
                className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'like' ? 'bg-green-100 text-green-600' : ''}`}
                title="Like"
                onClick={() => handleFeedback('like')}
              >
                <PiThumbsUp />
              </button>
              <button
                className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'dislike' ? 'bg-red-100 text-red-600' : ''}`}
                title="Dislike"
                onClick={() => handleFeedback('dislike')}
              >
                <PiThumbsDown />
              </button>
              <button
                className={`p-1 hover:bg-gray-100 rounded-full ${copied ? 'bg-green-100 text-green-600' : ''}`}
                onClick={handleCopy}
                title={copied ? "Copied!" : "Copy to clipboard"}
              >
                <PiCopy />
              </button>
              {/* <button
                className="p-1 hover:bg-gray-100 rounded-full"
                title="Regenerate response"
                onClick={handleRegenerate}
              >
                <PiArrowsCounterClockwise />
              </button> */}
              <button
                className="p-1 hover:bg-gray-100 rounded-full"
                title="Share response"
                onClick={handleShare}
              >
                <PiShareFat />
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  const renderCodeBlock = () => {
    const code = complexResponse?.code;
    const language = complexResponse?.language || 'plaintext';

    if (code) {
      return (
        <div className="mt-3 p-3 bg-gray-100 rounded-md overflow-x-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-gray-500">{language}</span>
            <button
              className="text-xs bg-gray-200 px-2 py-1 rounded hover:bg-gray-300"
              onClick={() => {
                navigator.clipboard.writeText(code);
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
              }}
            >
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <pre className="text-sm">
            <code className={`language-${language}`}>
              {code}
            </code>
          </pre>
        </div>
      );
    }
    return null;
  };

  const renderCommands = () => {
    if (
      complexResponse &&
      complexResponse.isCommandSuggestion &&
      complexResponse.commands?.length
    ) {
      return (
        <div className="mt-3">
          <p className="font-medium mb-2">Suggested commands:</p>
          <div className="flex flex-wrap gap-2">
            {complexResponse.commands.map((cmd: { command: string; label: string }, index: number) => (
              <button
                key={index}
                className="px-3 py-1 bg-primaryColor/10 text-primaryColor rounded-full text-xs hover:bg-primaryColor/20"
              >
                {cmd.label}
              </button>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };



  // Removed logDebugInfo function since we moved its logic directly into the useEffect

  // Call the debug function
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("BotReply Debug Info:", {
        aiResponseType: typeof aiResponse,
        aiResponseSample: typeof aiResponse === 'string'
          ? aiResponse.substring(0, 50) + (aiResponse.length > 50 ? '...' : '')
          : JSON.stringify(aiResponse),
        processedResponseSample: typeof responseContent === 'string'
          ? responseContent.substring(0, 50) + (responseContent.length > 50 ? '...' : '')
          : JSON.stringify(responseContent),
        hasSentenceAnalysis: typeof aiResponse === 'object' && aiResponse !== null &&
          Array.isArray(aiResponse.sentence_analysis) && aiResponse.sentence_analysis.length > 0
      });

      // Log sentence analysis data if available
      if (typeof aiResponse === 'object' && aiResponse !== null &&
          Array.isArray(aiResponse.sentence_analysis) && aiResponse.sentence_analysis.length > 0) {
        console.log("Sentence analysis data found:", aiResponse.sentence_analysis);
      }

      // Log related questions data if available
      if (typeof aiResponse === 'object' && aiResponse !== null &&
          Array.isArray(aiResponse.related_questions) && aiResponse.related_questions.length > 0) {
        console.log("Related questions data found:", aiResponse.related_questions);
      }
    }

    // Set scroll based on reply type
    if (replyType === 'loading' || replyType === 'response') {
      setScroll(true);
    }
  }, [aiResponse, responseContent, replyType, setScroll]);

  return (
    <div className="flex justify-start items-start gap-1 sm:gap-3 w-full max-w-[90%]">
      <Image src={logo} alt="AIQuill logo" className="max-sm:size-5 object-cover" width={32} height={32} />
      <div className="flex flex-col justify-start items-start gap-3 flex-1">
        {/* Debug info is now logged to console only */}
        <div className="flex justify-between items-center w-full">
          <p className="text-xs text-n100">QueryOne, {formattedTime}</p>

          {/* API Environment and Pinecone Indexes Dropdown */}
          <div className="relative" ref={dropdownRef}>
            {/* <div
              className={`text-xs mb-2 flex items-center gap-1 cursor-pointer ${apiEnvironment === 'development' ? 'text-blue-500' : 'text-gray-400'}`}
              onClick={() => apiEnvironment === 'development' && pineconeIndexes.length > 0 && setShowIndexes(!showIndexes)}
            >
              <span>API: {apiEnvironment === 'development' ? 'Development' : 'Production'}</span>
              {apiEnvironment === 'development' && pineconeIndexes.length > 0 && (
                <PiCaretUpDown className="inline-block" />
              )}
            </div> */}

            {/* Pinecone Indexes Dropdown */}
            {showIndexes && apiEnvironment === 'development' && pineconeIndexes.length > 0 && (
              <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 min-w-[200px]">
                <div className="py-1 px-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  Fiass Indexes
                </div>
                <div className="max-h-[200px] overflow-y-auto">
                  {pineconeIndexes.map((index: string, i: number) => (
                    <div
                      key={i}
                      className={`py-2 px-3 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}
                      onClick={() => handleIndexSelect(index)}
                    >
                      {index}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div 
          className="text-sm bg-primaryColor/5 py-4 px-6 border border-primaryColor/20 rounded-lg w-full sm:max-w-[90%] shadow-sm"
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", "Noto Sans Tamil", "Noto Sans Telugu", "Noto Sans Kannada", "Noto Sans Oriya", "Helvetica Neue", Arial, sans-serif',
            lineHeight: '1.7',
            letterSpacing: '0.01em',
            textRendering: 'optimizeLegibility',
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale'
          }}
        >
          {isAnimation || aiResponse === "__LOADING__" ? (
            <LoadingIndicator
              message="Generating answers for you"
              showSteps={true}
              showAvatar={false}
            />
          ) : (
            <div className="whitespace-pre-wrap">
              {responseContent && responseContent.trim() !== '' ? (
                // Display the response with Perplexity-style references if sentence analysis or related questions are available
                typeof aiResponse === 'object' && aiResponse !== null &&
                (Array.isArray(aiResponse.sentence_analysis) || Array.isArray(aiResponse.related_questions)) ? (
                  <ClientOnly fallback={<ReactMarkdown>{responseContent}</ReactMarkdown>}>
                    <PerplexityStyleResponse
                      text={responseContent}
                      sentenceAnalysis={aiResponse.sentence_analysis || []}
                      relatedQuestions={(() => {
                        console.log("🔍 BotReply - Passing related questions:", aiResponse.related_questions);
                        return aiResponse.related_questions || [];
                      })()}
                      onSelectQuestion={(question) => {
                        console.log("BotReply: Selected question:", question);
                        if (onSelectQuestion) {
                          onSelectQuestion(question);
                          // The ChatBox component will handle the automatic send
                          // No need to manually trigger Enter here
                        }
                      }}
                      selectedLanguage={selectedLanguage}
                    />
                  </ClientOnly>
                ) : (
                  // Check if we have a text object with related_questions
                  typeof aiResponse === 'object' && aiResponse !== null &&
                  typeof aiResponse.text === 'object' && aiResponse.text !== null &&
                  Array.isArray(aiResponse.text.related_questions) ? (
                    <ClientOnly fallback={<ReactMarkdown>{typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : responseContent}</ReactMarkdown>}>
                      <PerplexityStyleResponse
                        text={typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : responseContent}
                        sentenceAnalysis={aiResponse.text.sentence_analysis || []}
                        relatedQuestions={aiResponse.text.related_questions || []}
                        onSelectQuestion={(question) => {
                          console.log("BotReply: Selected question:", question);
                          if (onSelectQuestion) {
                            onSelectQuestion(question);
                            // The ChatBox component will handle the automatic send
                            // No need to manually trigger Enter here
                          }
                        }}
                        selectedLanguage={selectedLanguage}
                      />
                    </ClientOnly>
                  ) : (
                    // Otherwise just show the markdown
                    <ReactMarkdown>{responseContent}</ReactMarkdown>
                  )
                )
              ) : typeof safeAiResponse === 'string' && safeAiResponse.trim() !== '' ? (
                <ReactMarkdown>{safeAiResponse}</ReactMarkdown>
              ) : (
                <p>No response content available. Please try again.</p>
              )}

              {/* Render code blocks and commands */}
              <>
                {renderCodeBlock()}
                {renderCommands()}
              </>
            </div>
          )}
        </div>
        {!isSharedView && (
          <div className="flex justify-end items-center gap-2">
            {/* <button
              className={`p-1 hover:bg-gray-100 rounded-full ${isSpeaking ? 'bg-blue-100 text-blue-600' : ''}`}
              title={isSpeaking ? "Stop text to speech" : "Text to speech"}
              onClick={handleTextToSpeech}
            >
              <PiSpeakerHigh />
            </button> */}
            <button
              className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'like' ? 'bg-green-100 text-green-600' : ''}`}
              title="Like"
              onClick={() => handleFeedback('like')}
            >
              <PiThumbsUp />
            </button>
            <button
              className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'dislike' ? 'bg-red-100 text-red-600' : ''}`}
              title="Dislike"
              onClick={() => handleFeedback('dislike')}
            >
              <PiThumbsDown />
            </button>
            <button
              className={`p-1 hover:bg-gray-100 rounded-full ${copied ? 'bg-green-100 text-green-600' : ''}`}
              onClick={handleCopy}
              title={copied ? "Copied!" : "Copy to clipboard"}
            >
              <PiCopy />
            </button>
            {/* <button
              className="p-1 hover:bg-gray-100 rounded-full"
              title="Regenerate response"
              onClick={handleRegenerate}
            >
              <PiArrowsCounterClockwise />
            </button> */}
            <button
              className="p-1 hover:bg-gray-100 rounded-full"
              title="Share response"
              onClick={handleShare}
            >
              <PiShareFat />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default BotReply;
