#!/usr/bin/env python3
"""
Test script to debug Tamil API response
"""

import requests
import json

def test_tamil_api():
    """Test the Tamil query through the actual API"""
    
    # The problematic Tamil query
    tamil_query = "இந்திய ஒன்றியம் (ரயில்வே) மற்றும் இந்தியன் ஆயில் கார்ப்பரேஷன் லிமிடெட் இடையேயான சரக்கு கட்டண தகராறில் உள்ள முக்கிய உண்மை முரண்பாடு என்ன?"
    
    print("🧪 Testing Tamil API Response")
    print("=" * 50)
    print(f"Query: {tamil_query}")
    print()
    
    # Test the main financial_query endpoint
    url = "http://localhost:5001/financial_query"
    payload = {
        "query": tamil_query,
        "language": "Tamil"
    }
    
    try:
        print("📡 Sending request to main API...")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API Response received")
            print()
            
            # Print the AI response
            ai_response = result.get('ai_response', '')
            print(f"🤖 AI Response: {ai_response}")
            print()
            
            # Check for word repetition in the response
            if ai_response:
                words = ai_response.split()
                word_counts = {}
                for word in words:
                    word_counts[word] = word_counts.get(word, 0) + 1
                
                repeated_words = {word: count for word, count in word_counts.items() if count > 1}
                if repeated_words:
                    print("🔍 Repeated words found in response:")
                    for word, count in repeated_words.items():
                        print(f"   '{word}': {count} times")
                else:
                    print("✅ No word repetition found in response")
            
            # Print related questions
            related_questions = result.get('related_questions', [])
            print(f"\n📋 Related Questions ({len(related_questions)}):")
            for i, question in enumerate(related_questions, 1):
                print(f"   {i}. {question}")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the API")
        print("Make sure the Flask server is running on localhost:5001")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_tamil_api()