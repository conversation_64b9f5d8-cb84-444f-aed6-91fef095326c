#!/usr/bin/env python3
"""
Test script to debug Tamil query processing issues
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

# Import the functions directly from the full_code module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))
from full_code import detect_word_repetition, detect_text_corruption

def test_tamil_query():
    """Test the Tamil query that's causing issues"""
    
    # The problematic Tamil query
    tamil_query = "இந்திய ஒன்றியம் (ரயில்வே) மற்றும் இந்தியன் ஆயில் கார்ப்பரேஷன் லிமிடெட் இடையேயான சரக்கு கட்டண தகராறில் உள்ள முக்கிய உண்மை முரண்பாடு என்ன?"
    
    print("🧪 Testing Tamil Query Processing")
    print("=" * 50)
    print(f"Original Query: {tamil_query}")
    print(f"Query Length: {len(tamil_query)} characters")
    print()
    
    # Test word repetition detection
    print("1. Testing Word Repetition Detection:")
    word_corrupted, word_cleaned = detect_word_repetition(tamil_query)
    print(f"   Word Corrupted: {word_corrupted}")
    print(f"   Word Cleaned: {word_cleaned}")
    print()
    
    # Test text corruption detection
    print("2. Testing Text Corruption Detection:")
    is_corrupted, fully_cleaned, corruption_details = detect_text_corruption(tamil_query)
    print(f"   Is Corrupted: {is_corrupted}")
    print(f"   Fully Cleaned: {fully_cleaned}")
    print(f"   Corruption Details: {corruption_details}")
    print()
    
    # Test character diversity
    print("3. Character Analysis:")
    unique_chars = len(set(tamil_query))
    total_chars = len(tamil_query)
    diversity = unique_chars / total_chars if total_chars > 0 else 0
    print(f"   Unique Characters: {unique_chars}")
    print(f"   Total Characters: {total_chars}")
    print(f"   Character Diversity: {diversity:.3f}")
    print()
    
    # Check for Tamil characters
    import re
    tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
    tamil_chars = tamil_pattern.findall(tamil_query)
    print(f"   Tamil Characters Found: {len(tamil_chars)}")
    print(f"   Contains Tamil: {bool(tamil_chars)}")
    print()
    
    # Simulate the problematic output
    problematic_output = """IOCL SCC யூனியன் scc இந்தியா (ரயில்வே) மற்றும் இந்தியன் ஆயில் கார்ப்பரேஷன் லிமிடெட் (ஐ. ஓ. சி. எல்) iocl ஆகியவற்றுக்கு இடையேயான சரக்கு கட்டண தகராறின் உண்மை உண்மை முரண்பாடு முரண்பாடு சரக்கு சரக்கு ஒரு ஒரு "அதிக" அல்லது "சட்டவிரோத" சுற்றியுள்ளன."""
    
    print("4. Testing Problematic Output:")
    print(f"   Problematic Output: {problematic_output}")
    print()
    
    # Test corruption detection on problematic output
    prob_word_corrupted, prob_word_cleaned = detect_word_repetition(problematic_output)
    print(f"   Problem Word Corrupted: {prob_word_corrupted}")
    print(f"   Problem Word Cleaned: {prob_word_cleaned}")
    print()
    
    prob_is_corrupted, prob_fully_cleaned, prob_corruption_details = detect_text_corruption(problematic_output)
    print(f"   Problem Is Corrupted: {prob_is_corrupted}")
    print(f"   Problem Fully Cleaned: {prob_fully_cleaned}")
    print(f"   Problem Corruption Details: {prob_corruption_details}")

if __name__ == "__main__":
    test_tamil_query()