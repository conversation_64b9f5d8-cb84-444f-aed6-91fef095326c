/**
 * Translation API endpoint using @vitalets/google-translate-api
 * This endpoint handles translation requests from the Python backend
 */

import googleTranslateService from '../../services/googleTranslateService';

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ 
            success: false, 
            error: 'Method not allowed. Use POST.' 
        });
    }

    try {
        const { action, text, targetLanguage, sourceLang, responseData } = req.body;

        if (!action) {
            return res.status(400).json({
                success: false,
                error: 'Action is required. Use "detect", "translate", or "translateResponse"'
            });
        }

        switch (action) {
            case 'translateWithOdiaGenAI':
                if (!text || !targetLanguage) {
                    return res.status(400).json({
                        success: false,
                        error: 'Text and targetLanguage are required for OdiaGenAI translation'
                    });
                }

                try {
                    // Make request to Python backend's OdiaGenAI service
                    const backendResponse = await fetch('http://localhost:8000/translate-odiagenai', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            text: text,
                            source_lang: sourceLang || 'en',
                            target_lang: targetLanguage,
                            model_preference: 'auto'
                        })
                    });

                    if (!backendResponse.ok) {
                        throw new Error(`Backend request failed: ${backendResponse.status}`);
                    }

                    const backendData = await backendResponse.json();
                    return res.status(200).json(backendData);

                } catch (error) {
                    console.error('OdiaGenAI translation error:', error);
                    return res.status(500).json({
                        success: false,
                        error: 'OdiaGenAI translation service unavailable'
                    });
                }

            case 'translateToOriya':
                if (!text) {
                    return res.status(400).json({
                        success: false,
                        error: 'Text is required for Oriya translation'
                    });
                }

                try {
                    // Make request to Python backend's improved Oriya service
                    const backendResponse = await fetch('http://localhost:8000/translate-to-oriya', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            text: text,
                            method: 'auto'
                        })
                    });

                    if (!backendResponse.ok) {
                        throw new Error(`Backend request failed: ${backendResponse.status}`);
                    }

                    const backendData = await backendResponse.json();

                    if (backendData.success && backendData.data) {
                        return res.status(200).json({
                            success: true,
                            translated_text: backendData.data.translated_text,
                            method: backendData.data.method,
                            confidence: backendData.data.confidence,
                            processing_time: backendData.data.processing_time
                        });
                    } else {
                        return res.status(500).json({
                            success: false,
                            error: 'Translation failed'
                        });
                    }

                } catch (error) {
                    console.error('Improved Oriya translation error:', error);
                    return res.status(500).json({
                        success: false,
                        error: 'Improved Oriya translation service unavailable'
                    });
                }

            case 'detect':
                if (!text) {
                    return res.status(400).json({
                        success: false,
                        error: 'Text is required for language detection'
                    });
                }

                const detection = await googleTranslateService.detectLanguage(text);
                return res.status(200).json({
                    success: true,
                    ...detection
                });

            case 'translate':
                if (!text || !targetLanguage) {
                    return res.status(400).json({
                        success: false,
                        error: 'Text and targetLanguage are required for translation'
                    });
                }

                const translation = await googleTranslateService.translateText(
                    text, 
                    targetLanguage, 
                    sourceLang
                );
                return res.status(200).json(translation);

            case 'translateResponse':
                if (!responseData || !targetLanguage) {
                    return res.status(400).json({
                        success: false,
                        error: 'responseData and targetLanguage are required for response translation'
                    });
                }

                const translatedResponse = await googleTranslateService.translateFinancialResponse(
                    responseData, 
                    targetLanguage
                );
                return res.status(200).json({
                    success: true,
                    data: translatedResponse
                });

            default:
                return res.status(400).json({
                    success: false,
                    error: 'Invalid action. Use "detect", "translate", or "translateResponse"'
                });
        }

    } catch (error) {
        console.error('Translation API error:', error);
        return res.status(500).json({
            success: false,
            error: error.message || 'Internal server error'
        });
    }
}