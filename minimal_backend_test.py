#!/usr/bin/env python3
"""
Minimal backend test to check if <PERSON><PERSON><PERSON> starts
"""

from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/api/health', methods=['GET'])
def health():
    return jsonify({"status": "ok", "message": "Minimal backend is running"})

@app.route('/api/test', methods=['POST'])
def test():
    return jsonify({"success": True, "message": "Test endpoint working"})

if __name__ == '__main__':
    print("🚀 Starting minimal backend on port 5011...")
    app.run(host='0.0.0.0', port=5011, debug=True)
