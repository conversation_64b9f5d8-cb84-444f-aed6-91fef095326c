#!/usr/bin/env python3
"""
Test script to debug Node.js translation script directly.
"""

import subprocess
import json
import os

def test_nodejs_translation():
    """Test the Node.js translation script directly"""
    
    print("🧪 Testing Node.js Translation Script Directly")
    print("=" * 60)
    
    # Test query
    test_query = "పర్స్ಪెక్టివ్ X-31 ప్రోగ్రామ్ గురించి వివరించండి?"
    
    # Path to the Node.js script
    script_path = os.path.join(os.path.dirname(__file__), 'services', 'google_translate_script.js')
    
    print(f"📝 Test Query: {test_query}")
    print(f"📁 Script Path: {script_path}")
    print()
    
    # Test the Node.js script directly
    try:
        print("🔄 Running Node.js script...")
        result = subprocess.run([
            'node', script_path, 'translate',
            test_query, 'en', 'auto'
        ], capture_output=True, text=True, encoding='utf-8', timeout=30)
        
        print(f"📊 Return Code: {result.returncode}")
        print(f"📤 STDOUT: {result.stdout}")
        print(f"📤 STDERR: {result.stderr}")
        
        if result.returncode == 0 and result.stdout:
            try:
                response = json.loads(result.stdout)
                print(f"✅ JSON Response parsed successfully:")
                print(json.dumps(response, indent=2, ensure_ascii=False))
                
                # Check for capital word preservation
                translated_text = response.get('translatedText', '')
                if 'X-31' in translated_text:
                    print("✅ Capital words (X-31) preserved in translation")
                else:
                    print("⚠️ Capital words might not be preserved")
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw output: {result.stdout}")
        else:
            print("❌ Script execution failed or no output")
            
    except Exception as e:
        print(f"❌ Error running Node.js script: {e}")

if __name__ == "__main__":
    test_nodejs_translation()
