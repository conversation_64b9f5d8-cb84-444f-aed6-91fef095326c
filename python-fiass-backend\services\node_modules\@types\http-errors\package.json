{"name": "@types/http-errors", "version": "1.8.2", "description": "TypeScript definitions for http-errors", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-errors"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c2479b8b9d3c81b8d5e05a4e9af2847027b1274dadcdd92a5468f98c98449978", "typeScriptVersion": "3.8"}