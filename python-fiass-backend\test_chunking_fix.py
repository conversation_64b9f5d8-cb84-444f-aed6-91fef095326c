#!/usr/bin/env python3
"""
Simple test to verify MyMemory chunking fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mymemory_chunking():
    """Test MyMemory chunking functionality"""
    print("🧪 Testing MyMemory Chunking Fix")
    print("=" * 40)
    
    try:
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        # Create a text longer than 500 characters
        long_text = """
        Agricultural financing in India involves multiple schemes and institutions. The government provides various credit facilities through banks, cooperative societies, and microfinance institutions. Key schemes include Kisan Credit Card (KCC) which provides farmers with timely access to credit for their cultivation and other needs. The card allows farmers to purchase seeds, fertilizers, pesticides, and other inputs. It also covers post-harvest expenses and consumption requirements of farmer households. Agricultural Term Loans are provided for capital investments in agriculture such as purchase of tractors, pump sets, construction of wells, land development, and plantation activities.
        """.strip()
        
        print(f"📏 Testing text length: {len(long_text)} characters")
        print(f"Text preview: {long_text[:100]}...")
        
        # Test the chunking method directly
        chunks = translation_service._smart_chunk_text(long_text, 400)
        print(f"🔄 Text split into {len(chunks)} chunks:")
        
        for i, chunk in enumerate(chunks):
            print(f"   Chunk {i+1}: {len(chunk)} chars - {chunk[:50]}...")
        
        # Test MyMemory translation with chunking
        print(f"\n🌐 Testing MyMemory translation with chunking...")
        result = translation_service._translate_with_mymemory(long_text, 'en', 'te')
        
        if result:
            print(f"✅ MyMemory chunked translation successful!")
            print(f"📏 Result length: {len(result)} characters")
            print(f"Result preview: {result[:200]}...")
            print(f"Result ends with: ...{result[-100:]}")
        else:
            print("❌ MyMemory chunked translation failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_corruption_detection_fix():
    """Test that corruption detection is now less aggressive"""
    print("\n🧪 Testing Corruption Detection Fix")
    print("=" * 40)
    
    try:
        from full_code import detect_text_corruption
        
        # Test with Telugu text that has natural repetition
        telugu_text = """
        భారతదేశంలో వ్యవసాయ ఫైనాన్సింగ్ అనేక పథకాలు మరియు సంస్థలను కలిగి ఉంటుంది. ప్రభుత్వం బ్యాంకులు మరియు సహకార సంఘాలు మరియు మైక్రోఫైనాన్స్ సంస్థల ద్వారా వివిధ రుణ సౌకర్యాలను అందిస్తుంది. కిసాన్ క్రెడిట్ కార్డ్ పథకం రైతులకు వారి సాగు మరియు ఇతర అవసరాలకు సకాలంలో రుణ సదుపాయాన్ని అందిస్తుంది.
        """
        
        print(f"📏 Original text length: {len(telugu_text)} characters")
        
        is_corrupted, cleaned_text, details = detect_text_corruption(telugu_text)
        
        print(f"🔍 Corruption detection results:")
        print(f"   Is corrupted: {is_corrupted}")
        print(f"   Original length: {len(telugu_text)}")
        print(f"   Cleaned length: {len(cleaned_text)}")
        print(f"   Content preserved: {len(cleaned_text)/len(telugu_text):.1%}")
        
        if len(cleaned_text) >= len(telugu_text) * 0.95:
            print("✅ Corruption detection is now conservative - minimal content removed")
        else:
            print("⚠️ Corruption detection still removing too much content")
            
    except Exception as e:
        print(f"❌ Corruption test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run the simple tests"""
    print("🚀 Testing Translation Fixes")
    print("=" * 50)
    
    test_mymemory_chunking()
    test_corruption_detection_fix()
    
    print("\n📊 Fix Summary")
    print("=" * 50)
    print("1. MyMemory API now chunks long texts (>500 chars)")
    print("2. Corruption detection is ultra-conservative")
    print("3. Deep Translator handles long texts without issues")

if __name__ == '__main__':
    main()
