#!/usr/bin/env python3

import json
import subprocess
import sys

def test_node_script():
    """Test the Node.js translation script directly"""
    
    # Test data with Tamil response and sentence analysis
    test_data = {
        "ai_response": "நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டி: அடிப்படை அறிவைப் பெறுதல்: நிதி சந்தைகள் பற்றிய அடிப்படை புரிதலைப் பெறுங்கள்.",
        "related_questions": [
            "நிதி சந்தையில் முதலீடு செய்வது எப்படி?",
            "பங்குச் சந்தையில் முதலீடு செய்வது பாதுகாப்பானதா?"
        ],
        "sentence_analysis": [
            {
                "sentence": "நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டி:",
                "source_title": "Test Document",
                "source_type": "test-1",
                "file_id": "12345",
                "page": 1,
                "summary": "Test summary",
                "url": "test.xlsx"
            },
            {
                "sentence": "அடிப்படை அறிவைப் பெறுதல்: நிதி சந்தைகள் பற்றிய அடிப்படை புரிதலைப் பெறுங்கள்.",
                "source_title": "Test Document",
                "source_type": "test-2", 
                "file_id": "12345",
                "page": 2,
                "summary": "Test summary",
                "url": "test.xlsx"
            }
        ]
    }
    
    # Convert to JSON string
    data_json = json.dumps(test_data)
    target_lang = "Tamil"
    
    print(f"🧪 Testing Node.js script with Tamil to Tamil translation")
    print(f"📝 Test data: {data_json[:100]}...")
    print(f"🎯 Target language: {target_lang}")
    
    try:
        # Run the Node.js script
        result = subprocess.run([
            'node', 
            'python-fiass-backend/services/google_translate_script.js', 
            'translate-response',
            data_json, 
            target_lang
        ], capture_output=True, text=True, encoding='utf-8', timeout=60)
        
        print(f"\n📊 RESULTS:")
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            try:
                response = json.loads(result.stdout)
                print(f"\n✅ SUCCESS!")
                print(f"Response success: {response.get('success')}")
                if response.get('success'):
                    data = response.get('data', {})
                    ai_response = data.get('ai_response', '')
                    print(f"AI Response length: {len(ai_response)}")
                    print(f"AI Response preview: {ai_response[:200]}...")
                    print(f"Has fallback flag: {data.get('ai_response_fallback', False)}")
                    print(f"Error: {data.get('ai_response_error', 'None')}")
                else:
                    print(f"❌ Script returned success=false: {response.get('error')}")
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
        else:
            print(f"❌ Script failed with return code {result.returncode}")
            
    except subprocess.TimeoutExpired:
        print(f"❌ Script timed out")
    except Exception as e:
        print(f"❌ Error running script: {e}")

if __name__ == "__main__":
    test_node_script()
