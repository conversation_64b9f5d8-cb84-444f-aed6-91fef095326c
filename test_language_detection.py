#!/usr/bin/env python3
"""
Test language detection with Tamil text
"""

import sys
import os
sys.path.append('python-fiass-backend')

def test_language_detection():
    """Test language detection with Tamil text"""
    try:
        from services.enhanced_language_detector import enhanced_language_detector
        
        # Test Tamil query
        tamil_query = "ONGC நிறுவனத்தின் நடுவர் மனு (சிவில்) எண். 05/2022 ஏன்?"
        
        print("🧪 Testing language detection...")
        print(f"📝 Query: {tamil_query}")
        print(f"📏 Query length: {len(tamil_query)} characters")
        
        # Test enhanced language detection
        detected_language, confidence, scores = enhanced_language_detector.detect_language_with_confidence(tamil_query)
        
        print(f"🔍 Detected language: {detected_language}")
        print(f"📊 Confidence: {confidence:.3f}")
        print(f"🏆 All scores: {scores}")
        
        # Test basic Tamil detection
        import re
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        has_tamil = bool(tamil_pattern.search(tamil_query))
        tamil_chars = len(re.findall(r'[\u0B80-\u0BFF]', tamil_query))
        
        print(f"🔤 Contains Tamil characters: {has_tamil}")
        print(f"🔤 Tamil character count: {tamil_chars}")
        
        # Test encoding
        print(f"📝 Query bytes (UTF-8): {tamil_query.encode('utf-8')}")
        print(f"📝 Query repr: {repr(tamil_query)}")
        
        return detected_language == 'Tamil' and confidence > 0.1
        
    except Exception as e:
        print(f"❌ Error testing language detection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_encoding_issue():
    """Test if there's an encoding issue"""
    try:
        # Test different ways of representing Tamil text
        tamil_query = "ONGC நிறுவனத்தின் நடுவர் மனு (சிவில்) எண். 05/2022 ஏன்?"
        
        print("\n🧪 Testing encoding...")
        
        # Test JSON encoding
        import json
        json_str = json.dumps({"query": tamil_query}, ensure_ascii=False)
        print(f"📝 JSON (ensure_ascii=False): {json_str}")
        
        json_str_ascii = json.dumps({"query": tamil_query}, ensure_ascii=True)
        print(f"📝 JSON (ensure_ascii=True): {json_str_ascii}")
        
        # Test URL encoding
        import urllib.parse
        url_encoded = urllib.parse.quote(tamil_query)
        print(f"📝 URL encoded: {url_encoded}")
        
        # Test what happens when we decode the corrupted version
        corrupted = "ONGC ???????????? ?????? ??? (??????) ???. 05/2022 ????"
        print(f"📝 Corrupted version: {corrupted}")
        
        # Check if corrupted version has Tamil characters
        import re
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        has_tamil_corrupted = bool(tamil_pattern.search(corrupted))
        print(f"🔤 Corrupted version has Tamil: {has_tamil_corrupted}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing encoding: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting language detection tests...\n")
    
    # Test language detection
    detection_success = test_language_detection()
    
    # Test encoding
    encoding_success = test_encoding_issue()
    
    print(f"\n📊 Test Results:")
    print(f"   Language Detection: {'✅ PASS' if detection_success else '❌ FAIL'}")
    print(f"   Encoding Test: {'✅ PASS' if encoding_success else '❌ FAIL'}")
    
    if detection_success:
        print("\n✅ Language detection works correctly with proper Tamil text")
        print("❌ The issue is likely encoding corruption during HTTP request")
    else:
        print("\n❌ Language detection is failing even with proper Tamil text")
