#!/usr/bin/env python3
"""
Test the actual server response to see what's being returned
"""

import requests
import json

def test_server_response():
    """Test the server response"""
    
    print("🧪 TESTING ACTUAL SERVER RESPONSE")
    print("=" * 60)
    
    try:
        # Test data
        payload = {
            "query": "நிதி சந்தையில் முதலீடு செய்வது எப்படி?",
            "selected_language": "Tamil"
        }
        
        print(f"📝 Query: {payload['query']}")
        print(f"🌐 Language: {payload['selected_language']}")
        
        # Make request to server
        response = requests.post(
            'http://localhost:5010/financial_query',
            json=payload,
            timeout=60
        )
        
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check AI response
            ai_response = data.get('ai_response', '')
            print(f"🤖 AI Response Length: {len(ai_response)}")
            print(f"🤖 AI Response: {ai_response}")
            
            # Check for reference numbers
            ref_count = ai_response.count('[') + ai_response.count(']')
            has_refs = '[1]' in ai_response or '[2]' in ai_response or '[3]' in ai_response
            
            print(f"\n📊 REFERENCE ANALYSIS:")
            print(f"Bracket count: {ref_count}")
            print(f"Has reference numbers: {has_refs}")
            
            if has_refs:
                print("✅ Reference numbers found in server response!")
            else:
                print("❌ Reference numbers missing from server response!")
                
            # Check translation metadata
            translation_applied = data.get('translation_applied', False)
            translation_fallback = data.get('translation_fallback', False)
            reference_numbers_injected = data.get('reference_numbers_injected', False)
            
            print(f"\n📋 TRANSLATION METADATA:")
            print(f"Translation applied: {translation_applied}")
            print(f"Translation fallback: {translation_fallback}")
            print(f"Reference numbers injected: {reference_numbers_injected}")
            
            # Check sentence analysis
            sentence_analysis = data.get('sentence_analysis', [])
            print(f"📊 Sentence analysis count: {len(sentence_analysis)}")
            
            # Check related questions
            related_questions = data.get('related_questions', [])
            print(f"❓ Related questions count: {len(related_questions)}")
            
            # Show first few related questions
            for i, q in enumerate(related_questions[:3]):
                print(f"  {i+1}. {q}")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure the server is running on localhost:5010")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_server_response()
