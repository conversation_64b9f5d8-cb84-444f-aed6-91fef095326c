"""
Test script for Improved Oriya Translation
Quick test to verify the translation is working
"""

import sys
import os

# Add services to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'services'))

def test_improved_oriya_translation():
    """Test the improved Oriya translation service"""
    print("🧪 Testing Improved Oriya Translation Service")
    print("=" * 50)
    
    try:
        from services.improved_oriya_translation import improved_oriya_translator
        
        # Test cases
        test_texts = [
            "Hello, how are you?",
            "Good morning! Welcome to our service.",
            "The weather is very nice today.",
            "What is your name?",
            "Thank you for your help.",
            "The business is growing rapidly.",
            "Investment in technology is important.",
            "According to the document, the economic situation is improving."
        ]
        
        print("Testing different translation methods:")
        methods = ["dictionary", "mymemory", "google", "auto"]
        
        for method in methods:
            print(f"\n🔍 Testing method: {method.upper()}")
            print("-" * 30)
            
            for i, text in enumerate(test_texts[:3], 1):  # Test first 3 texts for each method
                print(f"\nTest {i}: {text}")
                
                result = improved_oriya_translator.translate_to_oriya(text, method)
                
                if result['success']:
                    print(f"✅ Success: {result['translated_text']}")
                    print(f"   Method: {result.get('method', 'unknown')}")
                    print(f"   Confidence: {result.get('confidence', 0):.2f}")
                else:
                    print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                    if 'translated_text' in result:
                        print(f"   Fallback: {result['translated_text']}")
        
        print("\n📊 Summary:")
        print("✅ Dictionary method should always work (fallback)")
        print("🌐 MyMemory method works without API keys")
        print("🔑 Google method requires API key for best results")
        print("🤖 Auto method tries all methods in order")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import improved Oriya service: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing improved Oriya service: {e}")
        return False

def test_flask_endpoint():
    """Test the Flask endpoint"""
    print("\n🌐 Testing Flask Endpoint")
    print("=" * 50)
    
    try:
        import requests
        
        url = "http://localhost:8000/translate-to-oriya"
        test_data = {
            "text": "Hello, welcome to our financial advisory service.",
            "method": "auto"
        }
        
        print(f"📝 Testing: {test_data['text']}")
        print(f"🔗 URL: {url}")
        
        response = requests.post(url, json=test_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                result_data = data.get('data', {})
                print(f"✅ Success: {result_data.get('translated_text', 'No translation')}")
                print(f"   Method: {result_data.get('method', 'unknown')}")
                print(f"   Confidence: {result_data.get('confidence', 0):.2f}")
                print(f"   Time: {result_data.get('processing_time', 0):.2f}s")
                return True
            else:
                print(f"❌ API Error: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Flask server not running on localhost:8000")
        print("   Start the server with: python full_code.py")
        return False
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def main():
    """Main test function"""
    print("🌟 Improved Oriya Translation Test Suite")
    print("=" * 60)
    
    # Test the service directly
    service_ok = test_improved_oriya_translation()
    
    # Test Flask endpoint
    endpoint_ok = test_flask_endpoint()
    
    print("\n🎯 Test Results Summary")
    print("=" * 60)
    print(f"🔧 Direct Service: {'✅ PASSED' if service_ok else '❌ FAILED'}")
    print(f"🌐 Flask Endpoint: {'✅ PASSED' if endpoint_ok else '❌ FAILED'}")
    
    if service_ok and endpoint_ok:
        print("\n🎉 All tests passed! The improved Oriya translation is working.")
        print("\n💡 Next steps:")
        print("1. Start your Flask server: python full_code.py")
        print("2. Start your Next.js frontend: npm run dev")
        print("3. Visit http://localhost:3000/oriya to test the translation")
    elif service_ok:
        print("\n⚠️ Service works but Flask endpoint failed.")
        print("Make sure your Flask server is running on port 8000.")
    else:
        print("\n❌ Service tests failed. Check the error messages above.")
        print("Make sure all required dependencies are installed.")

if __name__ == "__main__":
    main()
