"""
Test script to verify Hugging Face token is loaded correctly
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_hf_token():
    """Test if Hugging Face token is available"""
    print("🔍 Testing Hugging Face Token Configuration")
    print("=" * 50)
    
    # Check if token is in environment
    hf_token = os.getenv('HUGGINGFACE_API_TOKEN', '')
    
    if hf_token:
        print(f"✅ Token found: {hf_token[:10]}...{hf_token[-5:]}")
        print(f"📏 Token length: {len(hf_token)} characters")
        
        # Test if token format looks correct
        if hf_token.startswith('hf_') and len(hf_token) > 30:
            print("✅ Token format looks correct")
            
            # Test API connection
            try:
                import requests
                
                headers = {
                    "Authorization": f"Bearer {hf_token}",
                    "Content-Type": "application/json"
                }
                
                # Test with a simple model that definitely exists
                test_url = "https://api-inference.huggingface.co/models/facebook/nllb-200-distilled-600M"
                test_data = {
                    "inputs": "Hello world",
                    "parameters": {
                        "src_lang": "eng_Latn",
                        "tgt_lang": "ory_Orya"
                    }
                }
                
                print("🌐 Testing API connection...")
                response = requests.post(test_url, headers=headers, json=test_data, timeout=30)
                
                print(f"📡 Response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ API connection successful!")
                    print(f"🎯 Test translation result: {result}")
                    return True
                elif response.status_code == 503:
                    print("⏳ Model is loading, this is normal for first request")
                    print("🔄 Try again in a few minutes")
                    return True
                else:
                    print(f"❌ API error: {response.status_code}")
                    print(f"📄 Response: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ API test failed: {e}")
                return False
        else:
            print("❌ Token format looks incorrect")
            return False
    else:
        print("❌ No Hugging Face token found in environment")
        print("💡 Make sure HUGGINGFACE_API_TOKEN is set in .env file")
        return False

def test_improved_oriya_service():
    """Test the improved Oriya service with token"""
    print("\n🧪 Testing Improved Oriya Service")
    print("=" * 50)
    
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'services'))
        
        from services.improved_oriya_translation import improved_oriya_translator
        
        # Check if token is loaded in the service
        if improved_oriya_translator.hf_token:
            print(f"✅ Service has token: {improved_oriya_translator.hf_token[:10]}...{improved_oriya_translator.hf_token[-5:]}")
            
            # Test OdiaGenAI method specifically
            print("🤖 Testing OdiaGenAI method...")
            result = improved_oriya_translator._translate_with_odiagenai("Hello world")
            
            if result['success']:
                print(f"✅ OdiaGenAI translation successful: {result['translated_text']}")
                return True
            else:
                print(f"❌ OdiaGenAI translation failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("❌ Service doesn't have token loaded")
            return False
            
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Hugging Face Token Test Suite")
    print("=" * 60)
    
    # Test 1: Environment token
    token_ok = test_hf_token()
    
    # Test 2: Service integration
    service_ok = test_improved_oriya_service()
    
    print("\n🎯 Test Results Summary")
    print("=" * 60)
    print(f"🔑 Token Available: {'✅ PASSED' if token_ok else '❌ FAILED'}")
    print(f"🔧 Service Integration: {'✅ PASSED' if service_ok else '❌ FAILED'}")
    
    if token_ok and service_ok:
        print("\n🎉 All tests passed! OdiaGenAI should work now.")
    elif token_ok:
        print("\n⚠️ Token is available but service integration failed.")
        print("Check the service implementation.")
    else:
        print("\n❌ Token tests failed. Check your .env configuration.")
