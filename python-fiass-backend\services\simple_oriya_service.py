"""
Simple Oriya Translation Service
Works without external dependencies for basic translation
"""

import os
import requests
import logging
from typing import Dict, Any, List, Optional
import re

# Configure logging
logger = logging.getLogger(__name__)

class SimpleOriyaService:
    """Simple service for Oriya language processing without heavy dependencies"""
    
    def __init__(self):
        """Initialize the simple Oriya service"""
        self.hf_token = os.getenv('HUGGINGFACE_API_TOKEN', '*************************************')
        logger.info("✅ Simple Oriya Service initialized")
    
    def detect_oriya_content(self, text: str) -> bool:
        """
        Detect if text contains Oriya characters
        Uses Unicode range for Oriya script (U+0B00–U+0B7F)
        """
        if not text:
            return False
        
        # Oriya Unicode range: U+0B00 to U+0B7F
        oriya_pattern = re.compile(r'[\u0B00-\u0B7F]')
        oriya_matches = oriya_pattern.findall(text)
        
        # Consider it Oriya if more than 10% of characters are Oriya
        total_chars = len([c for c in text if c.isalpha()])
        if total_chars == 0:
            return False
        
        oriya_ratio = len(oriya_matches) / total_chars
        return oriya_ratio > 0.1
    
    def translate_to_oriya(self, english_text: str, context_query: str = "") -> Dict[str, Any]:
        """
        Translate English text to Oriya using multiple approaches
        """
        try:
            # First try HuggingFace API if token is available
            if self.hf_token:
                hf_result = self._try_huggingface_translation(english_text)
                if hf_result.get('success'):
                    return hf_result
            
            # Fallback to basic translation
            return self._basic_translation(english_text)
            
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'oriya_text': english_text
            }
    
    def _try_huggingface_translation(self, text: str) -> Dict[str, Any]:
        """Try translation using HuggingFace API"""
        models_to_try = [
            "Helsinki-NLP/opus-mt-en-or",
            "facebook/nllb-200-distilled-600M"
        ]
        
        headers = {
            "Authorization": f"Bearer {self.hf_token}",
            "Content-Type": "application/json"
        }
        
        for model_name in models_to_try:
            try:
                model_url = f"https://api-inference.huggingface.co/models/{model_name}"
                
                if "nllb" in model_name:
                    payload = {
                        "inputs": text,
                        "parameters": {
                            "src_lang": "eng_Latn",
                            "tgt_lang": "ory_Orya"
                        },
                        "options": {"wait_for_model": True}
                    }
                else:
                    payload = {
                        "inputs": text,
                        "options": {"wait_for_model": True}
                    }
                
                response = requests.post(model_url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if isinstance(result, list) and len(result) > 0:
                        if "translation_text" in result[0]:
                            translation = result[0]["translation_text"].strip()
                            if translation and translation != text:
                                logger.info(f"✅ HuggingFace translation successful with {model_name}")
                                return {
                                    'success': True,
                                    'oriya_text': translation,
                                    'method': f'huggingface_{model_name.split("/")[-1]}',
                                    'confidence': 0.8
                                }
                
            except Exception as e:
                logger.warning(f"HuggingFace model {model_name} failed: {e}")
                continue
        
        return {'success': False, 'error': 'All HuggingFace models failed'}
    
    def _basic_translation(self, text: str) -> Dict[str, Any]:
        """Basic word-by-word translation for common financial terms"""
        logger.info("🔄 Using basic translation...")
        
        # Comprehensive financial term translations
        translations = {
            # Basic financial terms
            'investment': 'ନିବେଶ',
            'investments': 'ନିବେଶ',
            'invest': 'ନିବେଶ କରନ୍ତୁ',
            'money': 'ଟଙ୍କା',
            'bank': 'ବ୍ୟାଙ୍କ',
            'banking': 'ବ୍ୟାଙ୍କିଂ',
            'loan': 'ଋଣ',
            'loans': 'ଋଣ',
            'savings': 'ସଞ୍ଚୟ',
            'save': 'ସଞ୍ଚୟ କରନ୍ତୁ',
            'market': 'ବଜାର',
            'markets': 'ବଜାର',
            'financial': 'ଆର୍ଥିକ',
            'finance': 'ଆର୍ଥିକ',
            'planning': 'ଯୋଜନା',
            'plan': 'ଯୋଜନା',
            'advice': 'ପରାମର୍ଶ',
            'help': 'ସାହାଯ୍ୟ',
            'portfolio': 'ପୋର୍ଟଫୋଲିଓ',
            'risk': 'ବିପଦ',
            'risks': 'ବିପଦ',
            'return': 'ଲାଭ',
            'returns': 'ଲାଭ',
            'fund': 'ପାଣ୍ଠି',
            'funds': 'ପାଣ୍ଠି',
            'stock': 'ଷ୍ଟକ୍',
            'stocks': 'ଷ୍ଟକ୍',
            'bond': 'ବଣ୍ଡ',
            'bonds': 'ବଣ୍ଡ',
            'insurance': 'ବୀମା',
            'tax': 'କର',
            'taxes': 'କର',
            'budget': 'ବଜେଟ୍',
            'income': 'ଆୟ',
            'expense': 'ଖର୍ଚ୍ଚ',
            'expenses': 'ଖର୍ଚ୍ଚ',
            'profit': 'ଲାଭ',
            'profits': 'ଲାଭ',
            'loss': 'କ୍ଷତି',
            'losses': 'କ୍ଷତି',
            'wealth': 'ସମ୍ପତ୍ତି',
            'asset': 'ସମ୍ପତ୍ତି',
            'assets': 'ସମ୍ପତ୍ତି',
            'debt': 'ଋଣ',
            'credit': 'କ୍ରେଡିଟ୍',
            'interest': 'ସୁଧ',
            'rate': 'ହାର',
            'rates': 'ହାର',
            'economy': 'ଅର୍ଥନୀତି',
            'economic': 'ଆର୍ଥିକ',
            'business': 'ବ୍ୟବସାୟ',
            'company': 'କମ୍ପାନୀ',
            'companies': 'କମ୍ପାନୀ',
            'security': 'ସୁରକ୍ଷା',
            'securities': 'ସିକ୍ୟୁରିଟି',
            'diversify': 'ବିବିଧୀକରଣ',
            'diversification': 'ବିବିଧୀକରଣ',
            'growth': 'ବୃଦ୍ଧି',
            'strategy': 'ରଣନୀତି',
            'strategies': 'ରଣନୀତି',
            'goal': 'ଲକ୍ଷ୍ୟ',
            'goals': 'ଲକ୍ଷ୍ୟ',
            'future': 'ଭବିଷ୍ୟତ',
            'retirement': 'ଅବସର',
            'pension': 'ପେନସନ୍',
            'mutual': 'ମ୍ୟୁଚୁଆଲ୍',
            'equity': 'ଇକ୍ୱିଟି',
            'dividend': 'ଡିଭିଡେଣ୍ଡ',
            'capital': 'ପୁଞ୍ଜି',
            'inflation': 'ମୁଦ୍ରାସ୍ଫୀତି'
        }
        
        # Apply translations
        translated_text = text
        for english, oriya in translations.items():
            # Case-insensitive replacement
            pattern = re.compile(re.escape(english), re.IGNORECASE)
            translated_text = pattern.sub(oriya, translated_text)
        
        # Add Oriya context phrases
        if any(term in text.lower() for term in ['investment', 'financial', 'money', 'planning']):
            translated_text = f"ଆର୍ଥିକ ପରାମର୍ଶ: {translated_text}"
        
        logger.info(f"✅ Basic translation completed")
        
        return {
            'success': True,
            'oriya_text': translated_text,
            'method': 'basic_translation',
            'confidence': 0.6
        }
    
    def enhance_oriya_response(self, english_response: str, oriya_query: str) -> Dict[str, Any]:
        """
        Enhance an English response for Oriya context
        """
        try:
            logger.info("🔄 Enhancing response for Oriya...")
            
            # Check if already in Oriya
            if self.detect_oriya_content(english_response):
                logger.info("✅ Response already contains Oriya content")
                return {
                    'success': True,
                    'oriya_response': english_response,
                    'method': 'already_oriya',
                    'confidence': 0.9
                }
            
            # Translate to Oriya
            translation_result = self.translate_to_oriya(english_response, oriya_query)
            
            if translation_result.get('success'):
                oriya_text = translation_result['oriya_text']
                logger.info(f"✅ Translation successful")
                return {
                    'success': True,
                    'oriya_response': oriya_text,
                    'method': f"simple_{translation_result.get('method', 'enhanced')}",
                    'confidence': translation_result.get('confidence', 0.7)
                }
            
            # Final fallback
            logger.warning("❌ All translation attempts failed")
            return {
                'success': False,
                'oriya_response': f"[ଅନୁବାଦ ବିଫଳ] {english_response}",
                'method': 'translation_failed',
                'error': 'Translation failed',
                'confidence': 0.1
            }
            
        except Exception as e:
            logger.error(f"Enhancement error: {e}")
            return {
                'success': False,
                'oriya_response': f"[ତ୍ରୁଟି] {english_response}",
                'method': 'enhancement_error',
                'error': str(e),
                'confidence': 0.1
            }

# Global instance
simple_oriya_service = SimpleOriyaService()
