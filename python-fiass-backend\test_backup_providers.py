#!/usr/bin/env python3
"""
Test backup translation providers when Google Translate is rate-limited
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mymemory_api():
    """Test MyMemory API for Telugu, Kannada, and Oriya"""
    print("🌐 Testing MyMemory API")
    print("=" * 50)
    
    try:
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        test_cases = [
            ('te', 'విద్యుత్ సరఫరా అవసరం', 'Telugu'),
            ('kn', 'ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ', 'Kannada'),
            ('or', 'ବିଦ୍ୟୁତ୍ ଯୋଗାଣ ଆବଶ୍ୟକ', 'Oriya')
        ]
        
        for lang_code, query, lang_name in test_cases:
            print(f"\n📝 Testing {lang_name} → English (MyMemory)")
            print(f"Original: {query}")
            
            try:
                result = translation_service._translate_with_mymemory(query, lang_code, 'en')
                if result:
                    print(f"✅ MyMemory Success: {result}")
                else:
                    print("❌ MyMemory returned None")
                    
            except Exception as e:
                print(f"❌ MyMemory Error: {e}")
        
        # Test English to regional languages
        english_text = "Electricity supply is needed for agricultural development"
        
        for lang_code, _, lang_name in test_cases:
            print(f"\n📝 Testing English → {lang_name} (MyMemory)")
            print(f"Original: {english_text}")
            
            try:
                result = translation_service._translate_with_mymemory(english_text, 'en', lang_code)
                if result:
                    print(f"✅ MyMemory Success: {result}")
                else:
                    print("❌ MyMemory returned None")
                    
            except Exception as e:
                print(f"❌ MyMemory Error: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ MyMemory test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_libretranslate_api():
    """Test LibreTranslate API for Telugu, Kannada, and Oriya"""
    print("\n🌐 Testing LibreTranslate API")
    print("=" * 50)
    
    try:
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        test_cases = [
            ('te', 'విద్యుత్ సరఫరా అవసరం', 'Telugu'),
            ('kn', 'ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ', 'Kannada'),
            ('or', 'ବିଦ୍ୟୁତ୍ ଯୋଗାଣ ଆବଶ୍ୟକ', 'Oriya')
        ]
        
        for lang_code, query, lang_name in test_cases:
            print(f"\n📝 Testing {lang_name} → English (LibreTranslate)")
            print(f"Original: {query}")
            
            try:
                result = translation_service._translate_with_libretranslate(query, lang_code, 'en')
                if result:
                    print(f"✅ LibreTranslate Success: {result}")
                else:
                    print("❌ LibreTranslate returned None")
                    
            except Exception as e:
                print(f"❌ LibreTranslate Error: {e}")
        
        # Test English to regional languages
        english_text = "Electricity supply is needed for agricultural development"
        
        for lang_code, _, lang_name in test_cases:
            print(f"\n📝 Testing English → {lang_name} (LibreTranslate)")
            print(f"Original: {english_text}")
            
            try:
                result = translation_service._translate_with_libretranslate(english_text, 'en', lang_code)
                if result:
                    print(f"✅ LibreTranslate Success: {result}")
                else:
                    print("❌ LibreTranslate returned None")
                    
            except Exception as e:
                print(f"❌ LibreTranslate Error: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ LibreTranslate test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deep_translator():
    """Test Deep Translator for Telugu, Kannada, and Oriya"""
    print("\n🌐 Testing Deep Translator")
    print("=" * 50)
    
    try:
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        test_cases = [
            ('te', 'విద్యుత్ సరఫరా అవసరం', 'Telugu'),
            ('kn', 'ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ', 'Kannada'),
            ('or', 'ବିଦ୍ୟୁତ୍ ଯୋଗାଣ ଆବଶ୍ୟକ', 'Oriya')
        ]
        
        for lang_code, query, lang_name in test_cases:
            print(f"\n📝 Testing {lang_name} → English (Deep Translator)")
            print(f"Original: {query}")
            
            try:
                result = translation_service._translate_with_deep_translator(query, lang_code, 'en')
                if result:
                    print(f"✅ Deep Translator Success: {result}")
                else:
                    print("❌ Deep Translator returned None")
                    
            except Exception as e:
                print(f"❌ Deep Translator Error: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Deep Translator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_translation():
    """Test the comprehensive translation method that tries all providers"""
    print("\n🌐 Testing Comprehensive Translation (All Providers)")
    print("=" * 50)
    
    try:
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        test_cases = [
            ('te', 'విద్యుత్ సరఫరా అవసరం', 'Telugu'),
            ('kn', 'ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ', 'Kannada'),
            ('or', 'ବିଦ୍ୟୁତ୍ ଯୋଗାଣ ଆବଶ୍ୟକ', 'Oriya')
        ]
        
        for lang_code, query, lang_name in test_cases:
            print(f"\n📝 Testing {lang_name} → English (Comprehensive)")
            print(f"Original: {query}")
            
            try:
                result = translation_service.translate_text(query, 'en', lang_code)
                if result:
                    print(f"✅ Comprehensive Success: {result}")
                else:
                    print("❌ Comprehensive returned None")
                    
            except Exception as e:
                print(f"❌ Comprehensive Error: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all backup provider tests"""
    print("🚀 Testing Backup Translation Providers")
    print("=" * 60)
    
    tests = [
        ("MyMemory API", test_mymemory_api),
        ("LibreTranslate API", test_libretranslate_api),
        ("Deep Translator", test_deep_translator),
        ("Comprehensive Translation", test_comprehensive_translation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")

if __name__ == '__main__':
    main()
