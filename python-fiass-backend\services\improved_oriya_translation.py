"""
Improved Oriya Translation Service
Combines multiple translation approaches for better Oriya language support
"""

import os
import requests
import json
import time
import logging
from typing import Dict, List, Optional
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedOriyaTranslationService:
    """
    Enhanced Oriya translation service that combines multiple approaches:
    1. OdiaGenAI models (when available)
    2. Google Translate API
    3. MyMemory API
    4. Enhanced dictionary-based translation
    """
    
    def __init__(self):
        self.hf_token = os.getenv('HUGGINGFACE_API_TOKEN', '')
        self.google_api_key = os.getenv('GOOGLE_TRANSLATE_API_KEY', '')
        
        # Enhanced Oriya dictionary for better fallback translations
        self.enhanced_oriya_dict = {
            # Basic words
            'hello': 'ନମସ୍କାର', 'hi': 'ନମସ୍କାର', 'good': 'ଭଲ', 'bad': 'ଖରାପ',
            'yes': 'ହଁ', 'no': 'ନା', 'please': 'ଦୟାକରି', 'thank you': 'ଧନ୍ୟବାଦ',
            'thanks': 'ଧନ୍ୟବାଦ', 'welcome': 'ସ୍ୱାଗତ', 'sorry': 'ଦୁଃଖିତ',
            
            # Common phrases
            'how are you': 'ଆପଣ କେମିତି ଅଛନ୍ତି', 'what is your name': 'ଆପଣଙ୍କ ନାମ କଣ',
            'my name is': 'ମୋର ନାମ', 'nice to meet you': 'ଆପଣଙ୍କୁ ଭେଟି ଖୁସି ଲାଗିଲା',
            'good morning': 'ସୁପ୍ରଭାତ', 'good evening': 'ସୁସନ୍ଧ୍ୟା', 'good night': 'ଶୁଭ ରାତ୍ରି',
            
            # Business and finance terms
            'business': 'ବ୍ୟବସାୟ', 'company': 'କମ୍ପାନୀ', 'market': 'ବଜାର', 'economy': 'ଅର୍ଥନୀତି',
            'investment': 'ନିବେଶ', 'profit': 'ଲାଭ', 'loss': 'କ୍ଷତି', 'money': 'ଟଙ୍କା',
            'bank': 'ବ୍ୟାଙ୍କ', 'loan': 'ଋଣ', 'interest': 'ସୁଧ', 'insurance': 'ବୀମା',
            'financial': 'ଆର୍ଥିକ', 'budget': 'ବଜେଟ', 'revenue': 'ଆୟ', 'expense': 'ଖର୍ଚ୍ଚ',
            
            # Technology terms
            'computer': 'କମ୍ପ୍ୟୁଟର', 'internet': 'ଇଣ୍ଟରନେଟ', 'website': 'ୱେବସାଇଟ',
            'software': 'ସଫ୍ଟୱେର', 'application': 'ଆପ୍ଲିକେସନ', 'data': 'ତଥ୍ୟ',
            'information': 'ସୂଚନା', 'technology': 'ପ୍ରଯୁକ୍ତି', 'digital': 'ଡିଜିଟାଲ',
            
            # Common verbs
            'is': 'ଅଛି', 'are': 'ଅଛନ୍ତି', 'was': 'ଥିଲା', 'were': 'ଥିଲେ',
            'have': 'ଅଛି', 'has': 'ଅଛି', 'had': 'ଥିଲା', 'will': 'ହେବ',
            'can': 'ପାରିବ', 'could': 'ପାରିଥିଲା', 'should': 'ଉଚିତ', 'would': 'ହେବ',
            'do': 'କର', 'does': 'କରେ', 'did': 'କଲା', 'go': 'ଯାଅ', 'come': 'ଆସ',
            'see': 'ଦେଖ', 'know': 'ଜାଣ', 'think': 'ଭାବ', 'want': 'ଚାହଁ',
            
            # Numbers
            'one': 'ଏକ', 'two': 'ଦୁଇ', 'three': 'ତିନି', 'four': 'ଚାରି', 'five': 'ପାଞ୍ଚ',
            'six': 'ଛଅ', 'seven': 'ସାତ', 'eight': 'ଆଠ', 'nine': 'ନଅ', 'ten': 'ଦଶ',
            'hundred': 'ଶହ', 'thousand': 'ହଜାର', 'million': 'ମିଲିୟନ', 'billion': 'ବିଲିୟନ',
            
            # Time and dates
            'today': 'ଆଜି', 'yesterday': 'ଗତକାଲି', 'tomorrow': 'ଆସନ୍ତାକାଲି',
            'now': 'ଏବେ', 'then': 'ତାପରେ', 'time': 'ସମୟ', 'year': 'ବର୍ଷ',
            'month': 'ମାସ', 'week': 'ସପ୍ତାହ', 'day': 'ଦିନ', 'hour': 'ଘଣ୍ଟା',
            
            # Places and directions
            'here': 'ଏଠାରେ', 'there': 'ସେଠାରେ', 'where': 'କେଉଁଠାରେ',
            'home': 'ଘର', 'office': 'କାର୍ଯ୍ୟାଳୟ', 'school': 'ବିଦ୍ୟାଳୟ',
            'hospital': 'ଡାକ୍ତରଖାନା', 'market': 'ବଜାର', 'city': 'ସହର',
            'country': 'ଦେଶ', 'world': 'ବିଶ୍ୱ', 'india': 'ଭାରତ', 'odisha': 'ଓଡ଼ିଶା'
        }
    
    def translate_to_oriya(self, text: str, method: str = "auto") -> Dict[str, any]:
        """
        Translate text to Oriya using the best available method
        
        Args:
            text: Text to translate
            method: Translation method ("auto", "odiagenai", "google", "mymemory", "dictionary")
        
        Returns:
            Dictionary with translation result
        """
        if not text or not text.strip():
            return {
                'success': False,
                'error': 'Empty text provided',
                'translated_text': text
            }
        
        text = text.strip()
        
        # Try methods in order of preference
        methods_to_try = []
        
        if method == "auto":
            methods_to_try = ["odiagenai", "google", "mymemory", "dictionary"]
        elif method in ["odiagenai", "google", "mymemory", "dictionary"]:
            methods_to_try = [method]
        else:
            methods_to_try = ["dictionary"]  # fallback
        
        last_error = None
        
        for translation_method in methods_to_try:
            try:
                if translation_method == "odiagenai":
                    result = self._translate_with_odiagenai(text)
                elif translation_method == "google":
                    result = self._translate_with_google(text)
                elif translation_method == "mymemory":
                    result = self._translate_with_mymemory(text)
                elif translation_method == "dictionary":
                    result = self._translate_with_dictionary(text)
                else:
                    continue
                
                if result['success']:
                    logger.info(f"✅ Translation successful with {translation_method}")
                    return result
                else:
                    last_error = result.get('error', 'Unknown error')
                    logger.warning(f"⚠️ {translation_method} failed: {last_error}")
                    
            except Exception as e:
                last_error = str(e)
                logger.error(f"❌ {translation_method} error: {e}")
                continue
        
        # If all methods fail, return dictionary translation as last resort
        return self._translate_with_dictionary(text)
    
    def _translate_with_odiagenai(self, text: str) -> Dict[str, any]:
        """Translate using OdiaGenAI models"""
        if not self.hf_token:
            return {
                'success': False,
                'error': 'Hugging Face API token not available',
                'translated_text': text
            }
        
        try:
            # Use the simpler IndicTrans model which is more reliable
            model_url = "https://api-inference.huggingface.co/models/ai4bharat/indictrans2-en-indic-1B"
            
            headers = {
                "Authorization": f"Bearer {self.hf_token}",
                "Content-Type": "application/json"
            }
            
            # IndicTrans2 expects specific format
            payload = {
                "inputs": f"English: {text}\nOdia:",
                "parameters": {
                    "max_new_tokens": 256,
                    "temperature": 0.3,
                    "do_sample": True
                },
                "options": {
                    "wait_for_model": True
                }
            }
            
            response = requests.post(model_url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    generated_text = result[0].get("generated_text", "")
                    # Extract Odia part
                    if "Odia:" in generated_text:
                        translation = generated_text.split("Odia:")[-1].strip()
                        if translation and translation != text:
                            return {
                                'success': True,
                                'translated_text': translation,
                                'method': 'odiagenai_indictrans',
                                'confidence': 0.85
                            }
            
            return {
                'success': False,
                'error': f'OdiaGenAI API error: {response.status_code}',
                'translated_text': text
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'OdiaGenAI error: {str(e)}',
                'translated_text': text
            }
    
    def _translate_with_google(self, text: str) -> Dict[str, any]:
        """Translate using Google Translate API"""
        try:
            if self.google_api_key:
                # Use official Google Translate API
                url = f"https://translation.googleapis.com/language/translate/v2?key={self.google_api_key}"
                payload = {
                    'q': text,
                    'source': 'en',
                    'target': 'or',
                    'format': 'text'
                }
                response = requests.post(url, data=payload, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    translation = result['data']['translations'][0]['translatedText']
                    return {
                        'success': True,
                        'translated_text': translation,
                        'method': 'google_api',
                        'confidence': 0.8
                    }
            else:
                # Use free Google Translate (less reliable but works)
                from deep_translator import GoogleTranslator
                translator = GoogleTranslator(source='en', target='or')
                translation = translator.translate(text)
                
                if translation and translation != text:
                    return {
                        'success': True,
                        'translated_text': translation,
                        'method': 'google_free',
                        'confidence': 0.75
                    }
            
            return {
                'success': False,
                'error': 'Google Translate failed',
                'translated_text': text
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Google Translate error: {str(e)}',
                'translated_text': text
            }
    
    def _translate_with_mymemory(self, text: str) -> Dict[str, any]:
        """Translate using MyMemory API"""
        try:
            url = f"https://api.mymemory.translated.net/get?q={requests.utils.quote(text)}&langpair=en|or"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('responseStatus') == 200:
                    translation = data['responseData']['translatedText']
                    if translation and translation != text and not translation.startswith('MYMEMORY WARNING'):
                        return {
                            'success': True,
                            'translated_text': translation,
                            'method': 'mymemory',
                            'confidence': 0.7
                        }
            
            return {
                'success': False,
                'error': 'MyMemory API failed',
                'translated_text': text
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'MyMemory error: {str(e)}',
                'translated_text': text
            }
    
    def _translate_with_dictionary(self, text: str) -> Dict[str, any]:
        """Translate using enhanced dictionary approach"""
        try:
            # Convert to lowercase for matching
            text_lower = text.lower()
            translated_parts = []
            
            # Split into sentences
            sentences = re.split(r'[.!?]+', text)
            
            for sentence in sentences:
                if not sentence.strip():
                    continue
                    
                sentence = sentence.strip()
                sentence_lower = sentence.lower()
                
                # Check for exact phrase matches first
                translated_sentence = None
                for phrase, translation in self.enhanced_oriya_dict.items():
                    if phrase in sentence_lower:
                        translated_sentence = sentence_lower.replace(phrase, translation)
                        break
                
                if not translated_sentence:
                    # Word-by-word translation
                    words = sentence.split()
                    translated_words = []
                    
                    for word in words:
                        word_clean = re.sub(r'[^\w\s]', '', word.lower())
                        if word_clean in self.enhanced_oriya_dict:
                            translated_words.append(self.enhanced_oriya_dict[word_clean])
                        else:
                            translated_words.append(word)  # Keep original if not found
                    
                    translated_sentence = ' '.join(translated_words)
                
                translated_parts.append(translated_sentence)
            
            final_translation = '. '.join(translated_parts)
            
            # Add prefix to indicate dictionary translation
            final_translation = f"[ଅନୁବାଦ] {final_translation}"
            
            return {
                'success': True,
                'translated_text': final_translation,
                'method': 'enhanced_dictionary',
                'confidence': 0.6
            }
            
        except Exception as e:
            return {
                'success': True,  # Always succeed with fallback
                'translated_text': f"[ଅନୁବାଦ ଚେଷ୍ଟା] {text}",
                'method': 'fallback',
                'confidence': 0.3,
                'error': str(e)
            }

# Global instance
improved_oriya_translator = ImprovedOriyaTranslationService()

# Convenience function
def translate_to_oriya(text: str, method: str = "auto") -> Dict[str, any]:
    """
    Convenience function to translate text to Oriya
    """
    return improved_oriya_translator.translate_to_oriya(text, method)
