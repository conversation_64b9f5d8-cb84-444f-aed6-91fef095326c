#!/usr/bin/env python3
"""
Test the full pipeline to debug reference number injection
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_full_pipeline():
    """Test the full pipeline with debug output"""
    
    print("🧪 TESTING FULL PIPELINE FOR REFERENCE INJECTION")
    print("=" * 60)
    
    try:
        # Import the translation service
        from services.google_translate_api_service_new import google_translate_service
        
        # Create test data that mimics what full_code.py would send
        test_response_data = {
            'ai_response': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல். அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
            'related_questions': [
                'நிதி சந்தையில் முதலீடு செய்வது எப்படி?',
                'பங்குச் சந்தையில் முதலீடு செய்வது பாதுகாப்பானதா?'
            ],
            'sentence_analysis': [
                {
                    'sentence': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல்.',
                    'sentiment': 'neutral',
                    'confidence': 0.8,
                    'url': 'https://example.com/page1',
                    'page': 1,
                    'vector_id': 'vec_001'
                },
                {
                    'sentence': 'அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
                    'sentiment': 'positive', 
                    'confidence': 0.9,
                    'url': 'https://example.com/page2',
                    'page': 2,
                    'vector_id': 'vec_002'
                }
            ],
            'query_metadata': {
                'original_query': 'நிதி சந்தையில் முதலீடு செய்வது எப்படி?',
                'query_language': 'Tamil'
            }
        }
        
        print(f"📝 Original AI Response: {test_response_data['ai_response']}")
        print(f"📊 Sentence Analysis Count: {len(test_response_data['sentence_analysis'])}")
        
        # Test the translation service
        print(f"\n🔄 Calling translate_financial_response...")
        result = google_translate_service.translate_financial_response(
            test_response_data, 
            'Tamil'
        )
        
        print(f"\n📊 TRANSLATION SERVICE RESULTS:")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            # Check both 'data' and 'response' keys
            data = result.get('data', {})
            response = result.get('response', {})
            
            print(f"📦 Data keys: {list(data.keys())}")
            print(f"📦 Response keys: {list(response.keys())}")
            
            # Check the AI response in both
            ai_response_data = data.get('ai_response', '')
            ai_response_response = response.get('ai_response', '')
            
            print(f"\n🤖 AI Response from 'data': {ai_response_data}")
            print(f"🤖 AI Response from 'response': {ai_response_response}")
            
            # Check for reference numbers
            for source, text in [('data', ai_response_data), ('response', ai_response_response)]:
                if text:
                    ref_count = text.count('[') + text.count(']')
                    has_refs = '[1]' in text or '[2]' in text
                    print(f"📊 {source}: Length={len(text)}, Brackets={ref_count}, Has refs={has_refs}")
                    
            # Check metadata
            metadata_data = data.get('query_metadata', {})
            metadata_response = response.get('query_metadata', {})
            
            print(f"\n📋 Metadata from 'data': {metadata_data}")
            print(f"📋 Metadata from 'response': {metadata_response}")
            
            # Check for fallback indicators
            fallback_data = metadata_data.get('translation_fallback', False)
            fallback_response = metadata_response.get('translation_fallback', False)
            
            print(f"⚠️ Fallback in 'data': {fallback_data}")
            print(f"⚠️ Fallback in 'response': {fallback_response}")
            
        else:
            print(f"❌ Translation failed: {result.get('error', 'Unknown error')}")
            
        # Now simulate what full_code.py does
        print(f"\n🔄 SIMULATING FULL_CODE.PY PROCESSING...")
        
        if result and result.get('success'):
            # This is the exact line from full_code.py line 4649
            updated_response_data = result.get('data', result.get('response', test_response_data))
            
            print(f"📦 Updated response keys: {list(updated_response_data.keys())}")
            final_ai_response = updated_response_data.get('ai_response', '')
            print(f"🎯 Final AI Response: {final_ai_response}")
            
            # Check for reference numbers in final response
            if '[1]' in final_ai_response or '[2]' in final_ai_response:
                print("✅ Reference numbers found in final response!")
            else:
                print("❌ Reference numbers missing from final response!")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_pipeline()
