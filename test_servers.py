#!/usr/bin/env python3
"""
Test if the servers are responding
"""

import requests
import json

def test_server(port, endpoint, description):
    """Test a server endpoint"""
    try:
        url = f"http://localhost:{port}{endpoint}"
        print(f"🧪 Testing {description} at {url}")
        
        response = requests.get(url, timeout=5)
        print(f"✅ Status: {response.status_code}")
        print(f"📄 Response: {response.text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False

def test_multilingual_query(port):
    """Test the multilingual query endpoint"""
    try:
        url = f"http://localhost:{port}/api/multilingual_financial_query"
        print(f"\n🧪 Testing multilingual query at {url}")
        
        # Test Tamil query
        tamil_query = "பங்குச் சந்தை என்ன?"
        response = requests.post(
            url,
            json={"query": tamil_query},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"✅ Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"📊 Success: {data.get('success', False)}")
            print(f"🌍 Language: {data.get('processing_language', 'Unknown')}")
            print(f"💬 Response preview: {data.get('ai_response', '')[:100]}...")
        else:
            print(f"❌ Error: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False

def main():
    print("🚀 Testing Running Servers")
    print("=" * 50)
    
    # Test super minimal Flask
    print("\n1. Super Minimal Flask (Port 5013):")
    test_server(5013, "/", "Super minimal Flask")
    
    # Test debug backend
    print("\n2. Debug Backend (Port 5012):")
    test_server(5012, "/api/health", "Debug backend health")
    test_multilingual_query(5012)
    
    # Test main backend
    print("\n3. Main Backend (Port 5010):")
    test_server(5010, "/api/health", "Main backend health")
    test_multilingual_query(5010)
    
    print("\n" + "=" * 50)
    print("🎯 Testing Complete!")

if __name__ == "__main__":
    main()
