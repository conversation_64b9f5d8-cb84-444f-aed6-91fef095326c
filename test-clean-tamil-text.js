/**
 * Test cleaning the provided Tamil text
 */

// Import the cleaning logic (simplified for testing)
class TextCleaningService {
  static cleanRepetitiveText(text) {
    if (!text || text.trim().length === 0) {
      return {
        hasRepetition: false,
        repetitionRatio: 0,
        repeatedWords: [],
        cleanedText: text,
        originalLength: 0,
        cleanedLength: 0
      };
    }

    const originalLength = text.length;
    
    // Split text into sentences for better processing
    const sentences = text.split(/[.!?।]+/).filter(s => s.trim().length > 0);
    const cleanedSentences = [];

    let totalRepeatedWords = [];

    for (const sentence of sentences) {
      const cleanedSentence = this.cleanSentenceRepetition(sentence.trim());
      cleanedSentences.push(cleanedSentence.cleaned);
      totalRepeatedWords.push(...cleanedSentence.repeatedWords);
    }

    const cleanedText = cleanedSentences.join('. ').trim();
    const cleanedLength = cleanedText.length;
    
    // Calculate repetition ratio
    const repetitionRatio = totalRepeatedWords.length > 0 ? 
      (originalLength - cleanedLength) / originalLength : 0;

    return {
      hasRepetition: totalRepeatedWords.length > 0,
      repetitionRatio,
      repeatedWords: [...new Set(totalRepeatedWords)], // Remove duplicates
      cleanedText: cleanedText + (cleanedText.endsWith('.') ? '' : '.'),
      originalLength,
      cleanedLength
    };
  }

  static cleanSentenceRepetition(sentence) {
    if (!sentence || sentence.trim().length === 0) {
      return { cleaned: sentence, repeatedWords: [] };
    }

    // Split into words, preserving punctuation
    const words = sentence.split(/\s+/).filter(word => word.trim().length > 0);
    const cleanedWords = [];
    const repeatedWords = [];
    
    // Track word frequency in a sliding window
    const windowSize = 5; // Look for repetition in the last 5 words
    
    for (let i = 0; i < words.length; i++) {
      const currentWord = words[i].toLowerCase().replace(/[^\w\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/g, ''); // Keep Tamil, Telugu, Kannada, Oriya characters
      
      if (currentWord.length === 0) {
        cleanedWords.push(words[i]);
        continue;
      }

      // Check if this word appears in the recent window
      let isRepetitive = false;
      const startIndex = Math.max(0, cleanedWords.length - windowSize);
      
      for (let j = startIndex; j < cleanedWords.length; j++) {
        const previousWord = cleanedWords[j].toLowerCase().replace(/[^\w\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/g, '');
        
        if (previousWord === currentWord && currentWord.length > 2) { // Only consider words longer than 2 characters
          isRepetitive = true;
          repeatedWords.push(currentWord);
          break;
        }
      }

      // Add word only if it's not repetitive
      if (!isRepetitive) {
        cleanedWords.push(words[i]);
      }
    }

    return {
      cleaned: cleanedWords.join(' '),
      repeatedWords
    };
  }
}

// Your original text
const originalText = `. வழங்கப்பட்ட ஆவணங்களின்படி, கட்டணம் மற்றும் சரக்கு கணக்கீடுகளில் தன்மையை உறுதி செய்வதற்காக தோற்றம் இலக்கு புள்ளிகளுக்கு 'வசூலிக்கக்கூடிய தூரத்தை கணக்கிடுவதற்கான ஒரு முறையை முறையை. ரவுண்டிங் பிழைகள் எவ்வாறு சட்ட வழிநடத்தும் என்பதை முறையின் விரிவான முறிவு: ### வசூலிக்கக்கூடிய தூரத்தைக் முறை முறை 1. இது வழியின்.இது ஒட்டுமொத்த. 3. ### சட்டரீதியான மோதல்களை எவ்வாறு வழிநடத்துகிறது 1..எனவே, முன்னர் சேகரிக்கப்பட்ட அதிகப்படியான கட்டணங்் திருப்பிச், அல்லது அல்லது வசூலிக்க முடியவில்லை 4). இருப்பினும், பயனர்கள் அடிப்படையில் குறைபாடுள்ள , வெறும் விஷயம் 91) வாதிடுவதில்.தோல்வியுற்றது 444 சட்டப்பூர்வமாக சட்டப்பூர்வமாக, முன்னணி தீர்ப்புகள் உரிமைகோருபவர்களுக்கு சாதகமாக 95). தவறானது அல்லது பிணைப்பு என்பதை மையமாகக் கொண்ட, சான்றுகள் அதிக கட்டணம் காட்டும்போது பெரும்பாலும். மேலும் விவரங்கள், ரயில்வே வாரியத்தைப் பார்க்கவும் பார்க்கவும் (07. 04.1891-1966 2024 (Pages 4, 87, 91, 93, 95).மேல்முறையீட்டு மேல்முறையீட்டு (கள்). 1891-1966 2024 (பக்கங்கள் 4, 87, 91, 93, 95).`;

console.log('🧹 Cleaning Tamil text with repetitive words...\n');

const result = TextCleaningService.cleanRepetitiveText(originalText);

console.log('📝 Original Text:');
console.log(originalText);
console.log('\n✨ Cleaned Text:');
console.log(result.cleanedText);
console.log('\n📊 Analysis:');
console.log(`- Has repetition: ${result.hasRepetition}`);
console.log(`- Repetition ratio: ${(result.repetitionRatio * 100).toFixed(1)}%`);
console.log(`- Repeated words: [${result.repeatedWords.join(', ')}]`);
console.log(`- Original length: ${result.originalLength} characters`);
console.log(`- Cleaned length: ${result.cleanedLength} characters`);
console.log(`- Characters removed: ${result.originalLength - result.cleanedLength}`);