from flask import Flask, request, jsonify
from flask_cors import CORS
import faiss
import json
import numpy as np
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from openai import OpenAI
from dotenv import load_dotenv
import os
import re
import datetime

# Load environment variables
load_dotenv()

# Configuration
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
# Use absolute paths relative to the project root
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
INDEX_PATH = os.path.join(BASE_DIR, "faiss_data", "default", "default.faiss")
METADATA_PATH = os.path.join(BASE_DIR, "faiss_data", "default", "default.json")

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Load FAISS index and metadata
try:
    faiss_index = faiss.read_index(INDEX_PATH)
    with open(METADATA_PATH, "r", encoding="utf-8") as f:
        metadata_store = json.load(f)
    print(f"✅ Loaded FAISS index with {faiss_index.ntotal} vectors")
except Exception as e:
    print(f"❌ Error loading FAISS index: {e}")
    faiss_index = None
    metadata_store = []

# Use multilingual embedding model (supports Tamil)
embedder = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L12-v2")

deepseek_client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")

def retrieve_from_faiss_tamil(query, k=5):
    if faiss_index is None or not metadata_store:
        return []

    query_vector = embedder.embed_documents([query])[0]
    query_embedding = np.array([query_vector]).astype("float32")
    faiss.normalize_L2(query_embedding)

    distances, indices = faiss_index.search(query_embedding, k)
    results = []

    for rank, idx in enumerate(indices[0]):
        if 0 <= idx < len(metadata_store):
            meta = metadata_store[idx]
            match = type('Match', (), {
                'score': float(distances[0][rank]),
                'metadata': {
                    'chunk_text': meta.get("chunk_text", ""),
                    'record_date': meta.get("record_date", "Unknown"),
                    'category': meta.get("category", "N/A"),
                    'url': meta.get("url", "N/A"),
                    'summary': meta.get("summary", "N/A")
                }
            })()
            results.append(match)
    return results

def generate_response_tamil(query, context_docs):
    context = "\n\n".join([doc.metadata.get('chunk_text', '') for doc in context_docs])
    messages = [
        {"role": "system", "content": "You are a helpful assistant that answers only in Tamil."},
        {"role": "user", "content": f"""கீழ்காணும் தகவல்களின் அடிப்படையில் இந்தக் கேள்விக்கு தமிழில் பதிலளிக்கவும்:

தகவல்:
{context}

கேள்வி: {query}
"""}
    ]
    response = deepseek_client.chat.completions.create(
        model="deepseek-chat",
        messages=messages,
        stream=False
    )
    return response.choices[0].message.content

def generate_related_questions(query, answer):
    prompt = f"""
கீழே உள்ள கேள்வி மற்றும் பதிலின் அடிப்படையில் தமிழில் 5 தொடர்புடைய தொடர்ந்த கேள்விகளை உருவாக்கவும்.

கேள்வி: {query}
பதில்: {answer}

தொடர்ந்த கேள்விகளை பட்டியலிடுங்கள்:"""
    messages = [{"role": "user", "content": prompt}]
    try:
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages
        )
        raw_text = response.choices[0].message.content
        lines = raw_text.strip().split('\n')
        questions = [
            re.sub(r"^\d+\.\s*", "", line).strip()
            for line in lines if re.search(r'\?', line)
        ]
        return questions[:5]
    except Exception as e:
        return [f"(தொடர்ந்த கேள்விகளை உருவாக்க முடியவில்லை: {str(e)})"]

def extract_sentences(text):
    return re.split(r'(?<=[.!?])\s+', text.strip())

def enrich_ai_response_with_urls(ai_response):
    sentences = extract_sentences(ai_response)
    enriched = []

    for sentence in sentences:
        if not sentence.strip():
            continue
        matches = retrieve_from_faiss_tamil(sentence, k=1)
        if matches:
            top_match = matches[0].metadata

            # Get source information
            source_url = top_match.get("url", "N/A")
            source_title = top_match.get("title", top_match.get("file_name", "Unknown"))
            source_type = top_match.get("source_type", top_match.get("vector_id", "unknown"))
            
            # Extract file_id and page information
            file_id = top_match.get("file_id", "Unknown")
            page_number = top_match.get("page", "Unknown")

            # Handle case when URL is N/A - use file_uploaded as URL and vector_id as source
            if source_url == "N/A" or not source_url:
                file_uploaded = top_match.get("file_uploaded")
                vector_id = top_match.get("vector_id")

                if file_uploaded:
                    source_url = file_uploaded
                    print(f"   🔄 URL was N/A, using file_uploaded as URL: {file_uploaded}")

                    # Update source_title to use vector_id if available
                    if vector_id:
                        source_title = vector_id
                        print(f"   🔄 Using vector_id as source: {vector_id}")

                    # Update source_type to indicate this is from uploaded file
                    if source_type == "unknown":
                        source_type = "excel_upload"

            # Create source-specific summary
            source_type_display = {
                'article': 'Article',
                'youtube': 'YouTube Video',
                'pdf': 'PDF Document',
                'document': 'Document',
                'audio': 'Audio File',
                'excel_upload': 'Excel Upload'
            }.get(source_type, source_type.title())

            # Create enhanced summary with source information
            enhanced_summary = f"Source: {source_type_display}"
            if source_title and source_title != "Unknown":
                enhanced_summary += f" - {source_title}"

            # Add original summary if available
            original_summary = top_match.get("summary", "")
            if original_summary and original_summary != "N/A":
                enhanced_summary += f" | {original_summary}"

            enriched.append({
                "sentence": sentence,
                "url": source_url,
                "summary": enhanced_summary,
                "source_type": source_type,
                "source_title": source_title,
                "file_id": file_id,
                "page": page_number
            })
        else:
            enriched.append({
                "sentence": sentence,
                "url": "Not found",
                "summary": "No summary found",
                "source_type": "unknown",
                "source_title": "Unknown",
                "file_id": "Unknown",
                "page": "Unknown"
            })
    return enriched

@app.route('/financial_query', methods=['POST'])
def handle_query():
    data = request.get_json()
    query = str(data.get("query", "")).strip()

    if not query:
        return jsonify({"error": "Query is required."}), 400

    matches = retrieve_from_faiss_tamil(query)
    retrieved_docs = []
    for i, match in enumerate(matches):
        metadata = match.metadata
        score = round(match.score * 100, 2)
        retrieved_docs.append({
            "rank": i + 1,
            "score": f"{score}%",
            "date": metadata.get('record_date', 'Unknown'),
            "category": metadata.get('category', 'N/A'),
            "text": metadata.get('chunk_text', 'No text')
        })

    ai_response = generate_response_tamil(query, matches)
    enriched_sentences = enrich_ai_response_with_urls(ai_response)
    related_questions = generate_related_questions(query, ai_response)

    return jsonify({
        "query": query,
        "retrieved_documents": retrieved_docs,
        "ai_response": ai_response,
        "sentence_analysis": enriched_sentences,
        "related_questions": related_questions
    })

if __name__ == '__main__':
    app.run(debug=True)
