#!/usr/bin/env python3
"""
Debug version of the backend to test if Flask starts properly
"""

import os
import sys
from flask import Flask, jsonify, request
from flask_cors import CORS
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🔧 Loading environment variables...")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
print(f"✅ DEEPSEEK_API_KEY loaded: {'Yes' if DEEPSEEK_API_KEY else 'No'}")

# Initialize Flask app
print("🔧 Initializing Flask app...")
app = Flask(__name__)
CORS(app)
print("✅ Flask app initialized with CORS")

@app.route('/api/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "ok",
        "message": "Debug backend is running",
        "deepseek_key_available": bool(DEEPSEEK_API_KEY)
    })

@app.route('/api/multilingual_financial_query', methods=['POST'])
def debug_multilingual_query():
    """Debug version of multilingual query endpoint"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        
        print(f"📝 Received query: {query}")
        
        # Simple language detection based on Unicode ranges
        def detect_language_simple(text):
            if any('\u0b80' <= char <= '\u0bff' for char in text):  # Tamil
                return 'ta'
            elif any('\u0c00' <= char <= '\u0c7f' for char in text):  # Telugu
                return 'te'
            elif any('\u0c80' <= char <= '\u0cff' for char in text):  # Kannada
                return 'kn'
            elif any('\u0b00' <= char <= '\u0b7f' for char in text):  # Oriya
                return 'or'
            else:
                return 'en'
        
        detected_lang = detect_language_simple(query)
        print(f"🌍 Detected language: {detected_lang}")
        
        # Mock response
        response = {
            "success": True,
            "processing_language": detected_lang,
            "ai_response": f"Mock response for {detected_lang} query: {query}",
            "sentiment_analysis": [
                {
                    "page": 1,
                    "vector_id": "mock_vector_1",
                    "file_uploaded": "mock_file.pdf",
                    "page_content": "Mock content for testing",
                    "file_id": "mock_file_id"
                }
            ],
            "related_questions": [
                "Mock related question 1?",
                "Mock related question 2?"
            ]
        }
        
        print(f"✅ Returning mock response for {detected_lang}")
        return jsonify(response)
        
    except Exception as e:
        print(f"❌ Error in debug endpoint: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Debug Backend...")
    print("📍 Server will run on http://0.0.0.0:5012")
    print("🔧 Debug mode: ON")
    print("=" * 50)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5012)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
