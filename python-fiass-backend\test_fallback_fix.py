#!/usr/bin/env python3
"""
Test the fallback fix for reference number injection
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fallback_fix():
    """Test the fallback fix"""
    
    print("🧪 TESTING FALLBACK FIX FOR REFERENCE INJECTION")
    print("=" * 60)
    
    try:
        from services.google_translate_api_service_new import google_translate_service
        
        # Clear the cache to force regeneration
        google_translate_service._script_path_cache = None
        
        # Force regeneration by deleting the script
        script_path = google_translate_service.node_script_path
        if os.path.exists(script_path):
            os.remove(script_path)
            print(f"🗑️ Deleted old script: {script_path}")
        
        # Regenerate the script
        new_script_path = google_translate_service._setup_node_script()
        print(f"📁 New script path: {new_script_path}")
        
        # Test data
        test_response_data = {
            'ai_response': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல். அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
            'related_questions': [
                'நிதி சந்தையில் முதலீடு செய்வது எப்படி?',
                'பங்குச் சந்தையில் முதலீடு செய்வது பாதுகாப்பானதா?'
            ],
            'sentence_analysis': [
                {
                    'sentence': 'நிதி சந்தையில் முதலீடு செய்வது பற்றிய விரிவான வழிகாட்டுதல்.',
                    'sentiment': 'neutral',
                    'confidence': 0.8
                },
                {
                    'sentence': 'அடிப்படை அறிவைப் பெறுதல் முக்கியம்.',
                    'sentiment': 'positive', 
                    'confidence': 0.9
                }
            ]
        }
        
        print("\n🔄 Testing translate_financial_response with fallback...")
        
        # Test translation (Tamil to Tamil with reference injection)
        result = google_translate_service.translate_financial_response(
            test_response_data, 
            'Tamil'
        )
        
        print(f"\n📊 RESULTS:")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            data = result.get('data', {})
            ai_response = data.get('ai_response', '')
            print(f"✅ AI Response length: {len(ai_response)}")
            print(f"📝 AI Response: {ai_response}")
            
            # Check for reference numbers
            if '[1]' in ai_response and '[2]' in ai_response:
                print("✅ Reference numbers found in response!")
            else:
                print("⚠️ Reference numbers not found in response")
                
            # Check for fallback indicators
            if data.get('ai_response_fallback'):
                print("✅ Fallback mechanism activated")
            
            if data.get('ai_response_error'):
                print(f"⚠️ Translation error (expected): {data.get('ai_response_error')}")
                
            related_questions = data.get('related_questions', [])
            print(f"📋 Related questions count: {len(related_questions)}")
                
        else:
            print(f"❌ Translation failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error testing fallback fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fallback_fix()
