"""
Test the fallback translation system without external API dependencies
"""

def test_fallback_translation():
    """Test the fallback translation directly"""
    
    print("🧪 Testing Fallback Translation System")
    print("=" * 50)
    
    try:
        # Import the service directly
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from services.odiagenai_dataset_service import OdiaGenAIDatasetService
        
        # Create service instance
        service = OdiaGenAIDatasetService()
        
        # Test English text that should be translated
        english_texts = [
            "Investment planning is important for financial security.",
            "You should diversify your portfolio and consider long-term goals.",
            "Savings and investment are key to building wealth.",
            "Financial planning helps you achieve your money goals."
        ]
        
        print("📝 Testing Fallback Translation:")
        print("-" * 40)
        
        for i, english_text in enumerate(english_texts, 1):
            print(f"\n{i}. English: {english_text}")
            
            # Test fallback translation
            result = service._fallback_translation(english_text)
            
            if result.get('success'):
                oriya_text = result['oriya_response']
                method = result['method']
                confidence = result['confidence']
                
                print(f"   Oriya: {oriya_text}")
                print(f"   Method: {method}")
                print(f"   Confidence: {confidence}")
                
                # Check if translation occurred
                if oriya_text != english_text:
                    print("   ✅ Translation applied")
                else:
                    print("   ⚠️  No translation applied")
            else:
                print("   ❌ Translation failed")
        
        print("\n" + "=" * 50)
        print("📊 Testing Language Detection:")
        print("-" * 40)
        
        test_texts = [
            ("English text", "This is an English sentence about investment."),
            ("Oriya text", "ନିବେଶ ବିଷୟରେ କିଛି ପରାମର୍ଶ ଦିଅନ୍ତୁ"),
            ("Mixed text", "Investment ନିବେଶ planning ଯୋଜନା")
        ]
        
        for label, text in test_texts:
            is_oriya = service.detect_oriya_content(text)
            print(f"{label}: '{text}' → Oriya: {is_oriya}")
        
        print("\n" + "=" * 50)
        print("🔧 Testing Enhancement Method:")
        print("-" * 40)
        
        # Test the enhancement method
        english_response = "Investment diversification is crucial for risk management. You should consider stocks, bonds, and mutual funds."
        oriya_query = "ନିବେଶ ବିଷୟରେ କିଛି ପରାମର୍ଶ ଦିଅନ୍ତୁ"
        
        print(f"English Response: {english_response}")
        print(f"Oriya Query: {oriya_query}")
        print()
        
        enhancement_result = service.enhance_oriya_response(english_response, oriya_query)
        
        print("Enhancement Result:")
        print(f"  Success: {enhancement_result.get('success')}")
        print(f"  Method: {enhancement_result.get('method')}")
        print(f"  Confidence: {enhancement_result.get('confidence')}")
        print(f"  Oriya Response: {enhancement_result.get('oriya_response', 'None')}")
        
        if enhancement_result.get('success'):
            oriya_response = enhancement_result['oriya_response']
            
            # Check for Oriya characters
            import re
            oriya_pattern = re.compile(r'[\u0B00-\u0B7F]')
            oriya_chars = len(oriya_pattern.findall(oriya_response))
            total_chars = len([c for c in oriya_response if c.isalpha()])
            
            if total_chars > 0:
                oriya_ratio = oriya_chars / total_chars
                print(f"  Oriya Character Ratio: {oriya_ratio:.2%}")
                
                if oriya_ratio > 0.3:
                    print("  ✅ SUCCESS: Significant Oriya content detected!")
                elif oriya_ratio > 0.1:
                    print("  ⚠️  PARTIAL: Some Oriya content detected")
                else:
                    print("  ❌ FAILED: No significant Oriya content")
            
        print("\n" + "=" * 50)
        print("🏆 FALLBACK TRANSLATION TEST COMPLETE")
        print("=" * 50)
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("The OdiaGenAI service may not be properly configured.")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_without_external_apis():
    """Test the system without relying on external APIs"""
    
    print("\n🔧 Testing System Without External APIs")
    print("=" * 50)
    
    # Mock the HuggingFace token to None to force fallback
    try:
        from services.odiagenai_dataset_service import OdiaGenAIDatasetService
        
        # Create service without HF token
        service = OdiaGenAIDatasetService()
        service.hf_token = None  # Force fallback mode
        
        print("🔄 Testing translation without HuggingFace API...")
        
        english_text = "Investment advice: Diversify your portfolio for better returns."
        oriya_query = "ନିବେଶ ବିଷୟରେ ପରାମର୍ଶ"
        
        result = service.enhance_oriya_response(english_text, oriya_query)
        
        print(f"Input: {english_text}")
        print(f"Output: {result.get('oriya_response', 'Failed')}")
        print(f"Method: {result.get('method', 'Unknown')}")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            print("✅ Fallback translation working!")
        else:
            print("❌ Fallback translation failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    # Test fallback translation
    success = test_fallback_translation()
    
    # Test without external APIs
    test_without_external_apis()
    
    if success:
        print("\n✅ The fallback translation system is working!")
        print("   This means Oriya queries should get some Oriya content,")
        print("   even if the advanced HuggingFace models are not available.")
    else:
        print("\n❌ The fallback translation system needs debugging.")
