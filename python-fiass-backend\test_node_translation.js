// Test script to debug Node.js translation issues
const args = process.argv.slice(2);
console.log('Arguments received:');
args.forEach((arg, index) => {
    console.log(`  [${index}]: ${arg}`);
});

if (args.length >= 2) {
    const command = args[0];
    const jsonStr = args[1];
    
    console.log(`Command: ${command}`);
    console.log(`JSON String: ${jsonStr}`);
    
    try {
        const parsed = JSON.parse(jsonStr);
        console.log('JSON parsed successfully:', parsed);
    } catch (error) {
        console.log('JSON parsing error:', error.message);
        console.log('JSON string length:', jsonStr.length);
        console.log('First 100 chars:', jsonStr.substring(0, 100));
    }
}
