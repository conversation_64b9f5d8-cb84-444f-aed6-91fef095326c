#!/usr/bin/env python3
"""
Test script to verify the multilingual integration works correctly.
Tests Tamil, Telugu, Kannada, and Oriya queries against the integrated backend.
"""

import requests
import json

# Backend URL
BACKEND_URL = "http://localhost:5010"

def test_language_query(query, language_name, expected_language_code):
    """Test a query in a specific language"""
    print(f"\n🧪 Testing {language_name} query...")
    print(f"Query: {query}")
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/multilingual_financial_query",
            json={"query": query},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {language_name} query successful!")
            print(f"Response language: {data.get('processing_language', 'Unknown')}")
            print(f"AI Response preview: {data.get('ai_response', '')[:100]}...")
            print(f"Sentiment analysis items: {len(data.get('sentiment_analysis', []))}")
            print(f"Related questions: {len(data.get('related_questions', []))}")
            return True
        else:
            print(f"❌ {language_name} query failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ {language_name} query error: {e}")
        return False

def main():
    """Run all language tests"""
    print("🚀 Testing Multilingual Integration")
    print("=" * 50)
    
    # Test queries in different languages
    test_cases = [
        {
            "query": "பங்குச் சந்தையின் தற்போதைய நிலை என்ன?",
            "language": "Tamil",
            "code": "ta"
        },
        {
            "query": "స్టాక్ మార్కెట్ ప్రస్తుత స్థితి ఎలా ఉంది?",
            "language": "Telugu", 
            "code": "te"
        },
        {
            "query": "ಷೇರು ಮಾರುಕಟ್ಟೆಯ ಪ್ರಸ್ತುತ ಸ್ಥಿತಿ ಹೇಗಿದೆ?",
            "language": "Kannada",
            "code": "kn"
        },
        {
            "query": "ଷେୟାର ବଜାରର ବର୍ତ୍ତମାନ ଅବସ୍ଥା କେମିତି?",
            "language": "Oriya",
            "code": "or"
        }
    ]
    
    results = []
    for test_case in test_cases:
        success = test_language_query(
            test_case["query"], 
            test_case["language"], 
            test_case["code"]
        )
        results.append((test_case["language"], success))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    for language, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{language}: {status}")
    
    successful_tests = sum(1 for _, success in results if success)
    print(f"\nOverall: {successful_tests}/{len(results)} tests passed")
    
    if successful_tests == len(results):
        print("🎉 All multilingual integration tests passed!")
    else:
        print("⚠️ Some tests failed. Check the logs above.")

if __name__ == "__main__":
    main()
