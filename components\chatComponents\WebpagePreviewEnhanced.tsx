import React, { useState, useEffect, memo } from 'react';
import { PiGlobe, PiArrowSquareOut, PiSpinner, PiLink, PiInfo, PiFile, PiFileText, PiDatabase, PiHash, PiBookOpen, PiLayers, PiChevronDown, PiChevronUp, PiCopy } from 'react-icons/pi';

interface WebpagePreviewProps {
  url: string;
  summary?: string;
  source_title?: string;
  source_type?: string;
  file_id?: string;
  page?: string;
  page_content?: string;
  vector_id?: string;
  file_uploaded?: string;
  referenceNumber: number;
}

const WebpagePreviewEnhanced: React.FC<WebpagePreviewProps> = memo(({
  url,
  summary,
  source_title,
  source_type,
  file_id,
  page,
  page_content,
  vector_id,
  file_uploaded,
  referenceNumber
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [domainName, setDomainName] = useState<string>('');

  // Check if URL is valid (not N/A or Not found)
  const isValidUrl = url && url !== 'N/A' && url !== 'Not found' && url.trim() !== '';
  
  // Check if this is FAISS data
  const hasFaissData = vector_id || file_uploaded || page_content || file_id;

  // Extract domain name for display
  useEffect(() => {
    if (isValidUrl) {
      try {
        const domain = new URL(url).hostname;
        setDomainName(domain.replace('www.', ''));
      } catch (error) {
        setDomainName(url);
      }
    } else {
      setDomainName('');
    }
  }, [url, isValidUrl]);

  const handleVisitClick = () => {
    if (isValidUrl) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatSummary = (text: string) => {
    if (!text) return '';
    return text.replace(/^(Source:|Summary:|Content:)\s*/i, '').trim();
  };

  const getSourceIcon = () => {
    if (hasFaissData) return <PiDatabase className="text-purple-500 text-lg" />;
    if (isValidUrl) return <PiGlobe className="text-blue-500 text-lg" />;
    return <PiFile className="text-gray-500 text-lg" />;
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
      {/* Header with reference number and source info */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-md">
              {referenceNumber}
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-1 line-clamp-1">
                {source_title || file_uploaded || domainName || 'Reference Source'}
              </h3>
              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                {getSourceIcon()}
                <span>
                  {hasFaissData ? 'Knowledge Base' : isValidUrl ? 'Web Source' : 'Document'}
                </span>
                {source_type && (
                  <>
                    <span>•</span>
                    <span>{source_type}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          {(hasFaissData || page_content || summary) && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 hover:bg-white/50 dark:hover:bg-gray-700/50 rounded-lg transition-colors"
              title={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              {isExpanded ? (
                <PiChevronUp className="text-gray-600 dark:text-gray-400" />
              ) : (
                <PiChevronDown className="text-gray-600 dark:text-gray-400" />
              )}
            </button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4">
        {/* Quick Info Row */}
        <div className="flex items-center gap-4 mb-3 text-xs">
          {file_id && (
            <div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              <PiHash className="text-gray-500" />
              <span className="text-gray-700 dark:text-gray-300">ID: {file_id}</span>
            </div>
          )}
          {page && (
            <div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              <PiBookOpen className="text-gray-500" />
              <span className="text-gray-700 dark:text-gray-300">Page: {page}</span>
            </div>
          )}
          {vector_id && (
            <div className="flex items-center gap-1 bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded">
              <PiLayers className="text-purple-500" />
              <span className="text-purple-700 dark:text-purple-300">Vector: {vector_id}</span>
            </div>
          )}
        </div>

        {/* URL Display */}
        {isValidUrl && (
          <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg mb-3">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <PiLink className="text-blue-500 flex-shrink-0" />
              <span className="text-xs text-gray-600 dark:text-gray-400 truncate font-mono">
                {domainName || url}
              </span>
            </div>
            <button
              onClick={handleVisitClick}
              className="flex items-center gap-1 text-white bg-blue-500 hover:bg-blue-600 px-3 py-1.5 rounded-lg text-xs font-medium transition-colors"
            >
              <span>Visit</span>
              <PiArrowSquareOut />
            </button>
          </div>
        )}

        {/* Summary */}
        {summary && formatSummary(summary) && (
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-600/50 p-3 rounded-lg mb-3 border-l-4 border-blue-500/30">
            <div className="flex items-start gap-2">
              <PiInfo className="text-blue-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-600 dark:text-gray-300 mb-1">Content Preview</p>
                <p className="text-xs text-gray-700 dark:text-gray-200 leading-relaxed">
                  {formatSummary(summary)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Expanded Details */}
        {isExpanded && (
          <div className="space-y-3 border-t border-gray-200 dark:border-gray-700 pt-3">
            {/* FAISS Vector Data */}
            {(vector_id || file_uploaded) && (
              <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="flex items-center gap-2 mb-2">
                  <PiDatabase className="text-purple-600 dark:text-purple-400" />
                  <span className="text-xs font-medium text-purple-700 dark:text-purple-300">FAISS Vector Data</span>
                </div>
                <div className="space-y-2 text-xs">
                  {vector_id && (
                    <div className="flex items-center justify-between">
                      <span className="text-purple-600 dark:text-purple-400">Vector ID:</span>
                      <div className="flex items-center gap-1">
                        <span className="text-purple-700 dark:text-purple-300 font-mono">{vector_id}</span>
                        <button onClick={() => copyToClipboard(vector_id)} className="text-purple-500 hover:text-purple-700">
                          <PiCopy />
                        </button>
                      </div>
                    </div>
                  )}
                  {file_uploaded && (
                    <div className="flex items-center justify-between">
                      <span className="text-purple-600 dark:text-purple-400">File:</span>
                      <span className="text-purple-700 dark:text-purple-300">{file_uploaded}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Page Content */}
            {page_content && page_content.trim() !== "" && (
              <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-2 mb-2">
                  <PiFileText className="text-green-600 dark:text-green-400" />
                  <span className="text-xs font-medium text-green-700 dark:text-green-300">Page Content</span>
                </div>
                <div className="text-xs text-green-700 dark:text-green-200 leading-relaxed max-h-32 overflow-y-auto">
                  {page_content.length > 300 ? `${page_content.substring(0, 300)}...` : page_content}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

WebpagePreviewEnhanced.displayName = 'WebpagePreviewEnhanced';

export default WebpagePreviewEnhanced;
