#!/usr/bin/env python3
"""
Test script to verify Tamil processing and corruption detection
"""

import sys
import os
sys.path.append('python-fiass-backend')

def test_tamil_corruption_detection():
    """Test Tamil corruption detection with sample text"""
    try:
        from full_code import detect_text_corruption, clean_multilingual_response
        
        # Sample Tamil text (similar to what might be returned)
        tamil_text = """
        ஆன்பிசி (ONGC) மூலம் தாக்கல் செய்யப்பட்ட நடுவர் மனு (சிவில்) எண். 05/2022 என்பது 
        எண்ணெய் மற்றும் இயற்கை எரிவாயு நிறுவனம் லிமிடெட் (ONGC) மூலம் தாக்கல் செய்யப்பட்ட 
        ஒரு நடுவர் மனுவாகும். இந்த மனு பொதுவாக ஒப்பந்த தகராறுகள், வணிக சர்ச்சைகள் 
        அல்லது சட்ட விவகாரங்களுடன் தொடர்புடையதாக இருக்கலாம்.
        """
        
        print("🧪 Testing Tamil corruption detection...")
        print(f"📏 Original text length: {len(tamil_text)} characters")
        
        # Test corruption detection
        is_corrupted, cleaned_text, details = detect_text_corruption(tamil_text)
        
        print(f"🔍 Corruption detected: {is_corrupted}")
        print(f"📏 Cleaned text length: {len(cleaned_text)} characters")
        print(f"📊 Content preserved: {len(cleaned_text)/len(tamil_text)*100:.1f}%")
        print(f"🔧 Corruption details: {details}")
        
        # Test multilingual response cleaning
        sample_response = {
            "ai_response": tamil_text,
            "related_questions": [
                "ONGC நிறுவனம் என்றால் என்ன?",
                "நடுவர் மனு எப்படி தாக்கல் செய்வது?",
                "எண்ணெய் நிறுவன சர்ச்சைகள் எப்படி தீர்க்கப்படுகின்றன?"
            ]
        }
        
        print("\n🧪 Testing multilingual response cleaning...")
        cleaned_response = clean_multilingual_response(sample_response, 'ta')
        
        print(f"📏 Original response length: {len(sample_response['ai_response'])} characters")
        print(f"📏 Cleaned response length: {len(cleaned_response['ai_response'])} characters")
        print(f"📊 Content preserved: {len(cleaned_response['ai_response'])/len(sample_response['ai_response'])*100:.1f}%")
        
        if 'cleaning_metadata' in cleaned_response:
            print(f"🔧 Cleaning metadata: {cleaned_response['cleaning_metadata']}")
        else:
            print("✅ No cleaning applied - text was already clean")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing Tamil processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tamil_router():
    """Test Tamil query router"""
    try:
        from services.tamil_query_router import tamil_router
        
        print("\n🧪 Testing Tamil query router...")
        
        # Test Tamil query
        tamil_query = "ONGC நிறுவனத்தின் நடுவர் மனு ஏன் தாக்கல் செய்யப்பட்டது?"
        
        should_route = tamil_router.should_route_to_tamil_api(tamil_query)
        print(f"🔀 Should route to Tamil API: {should_route}")
        
        if should_route:
            print("🌏 Testing Tamil API routing...")
            try:
                response = tamil_router.route_tamil_query(tamil_query)
                print(f"✅ Tamil API response received")
                print(f"📏 Response length: {len(str(response))} characters")
                
                if 'error' in response:
                    print(f"⚠️ Tamil API error: {response['error']}")
                else:
                    print("✅ Tamil API response successful")
                    
            except Exception as e:
                print(f"❌ Tamil API routing failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Tamil router: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Tamil processing tests...\n")
    
    # Test corruption detection
    test1_success = test_tamil_corruption_detection()
    
    # Test router
    test2_success = test_tamil_router()
    
    print(f"\n📊 Test Results:")
    print(f"   Corruption Detection: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Tamil Router: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Tamil processing should work correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
