#!/usr/bin/env python3
"""
Test script for mixed language query support and capital word preservation.
Tests the specific user case: "పర్స్ಪెక్టివ్ X-31 ప్రోగ్రామ్ గురించి వివరించండి?"
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mixed_language_query():
    """Test mixed language query with Telugu + English"""
    
    print("🧪 Testing Mixed Language Query Support")
    print("=" * 60)
    
    # Test query with Telugu + English (user's specific case)
    mixed_query = "పర్స్ಪెక్టివ్ X-31 ప్రోగ్రామ్ గురించి వివరించండి?"
    print(f"📝 Test Query: {mixed_query}")
    print()
    
    # Test 1: New Google Translate API Service
    print("🔬 Test 1: New Google Translate API Service")
    print("-" * 40)
    
    try:
        from services.google_translate_api_service_new import google_translate_service
        
        if google_translate_service.is_service_available():
            print("✅ New Google Translate API service is available")
            
            # Test translation to English
            result = google_translate_service.translate_text(mixed_query, 'en', 'auto')
            
            print(f"🔍 Translation Result:")
            print(f"   Original: {mixed_query}")
            print(f"   Translated: {result.get('translated_text', 'N/A')}")
            print(f"   Source Language: {result.get('source_language', 'N/A')}")
            print(f"   Success: {result.get('success', False)}")
            
            # Check for CAPWORD tokens
            translated_text = result.get('translated_text', '')
            if 'CAPWORD' in translated_text or '__capital_word_' in translated_text:
                print("❌ CAPWORD tokens found in translation!")
                print(f"   Problematic text: {translated_text}")
            else:
                print("✅ No CAPWORD tokens found - capital words preserved correctly")
                
        else:
            print("❌ New Google Translate API service not available")
            
    except ImportError as e:
        print(f"❌ Failed to import new service: {e}")
    except Exception as e:
        print(f"❌ Error testing new service: {e}")
    
    print()
    
    # Test 2: Backend API Endpoint
    print("🔬 Test 2: Backend API Endpoint (/api/translate)")
    print("-" * 40)
    
    try:
        import requests
        
        # Test the backend API endpoint
        api_url = "http://localhost:5010/api/translate"
        payload = {
            "text": mixed_query,
            "source_lang": "auto",
            "target_lang": "en"
        }
        
        print(f"🌐 Making API request to: {api_url}")
        print(f"📦 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(api_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API request successful")
            print(f"🔍 API Response:")
            print(f"   Status: {result.get('status', 'N/A')}")
            
            data = result.get('data', {})
            translated_text = data.get('translated_text', '')
            
            print(f"   Original: {data.get('original_text', 'N/A')}")
            print(f"   Translated: {translated_text}")
            print(f"   Provider: {data.get('translation_provider', 'N/A')}")
            
            # Check for CAPWORD tokens
            if 'CAPWORD' in translated_text or '__capital_word_' in translated_text:
                print("❌ CAPWORD tokens found in API response!")
                print(f"   Problematic text: {translated_text}")
            else:
                print("✅ No CAPWORD tokens found in API response")
                
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend API - is the server running?")
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")
    
    print()
    
    # Test 3: Language Detection
    print("🔬 Test 3: Language Detection")
    print("-" * 40)
    
    try:
        from services.google_translate_api_service_new import google_translate_service
        
        # Test language detection for mixed query
        detection_result = google_translate_service.detect_language(mixed_query)
        
        print(f"🔍 Language Detection Result:")
        print(f"   Query: {mixed_query}")
        print(f"   Detected Language: {detection_result.get('language', 'N/A')}")
        print(f"   Confidence: {detection_result.get('confidence', 'N/A')}")
        print(f"   Success: {detection_result.get('success', False)}")
        
    except Exception as e:
        print(f"❌ Error testing language detection: {e}")
    
    print()
    print("🏁 Mixed Language Query Test Complete")
    print("=" * 60)

if __name__ == "__main__":
    test_mixed_language_query()
