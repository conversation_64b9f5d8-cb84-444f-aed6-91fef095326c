"""
OdiaGenAI Dataset Service
Uses the pre-trained Odia dataset from HuggingFace to ensure proper Oriya responses
Dataset: https://huggingface.co/datasets/OdiaGenAIdata/pre_train_odia_data_processed
"""

import os
import requests
import logging
from typing import Dict, Any, List, Optional
import re

# Optional imports - gracefully handle missing dependencies
try:
    from datasets import load_dataset
    DATASETS_AVAILABLE = True
except ImportError:
    DATASETS_AVAILABLE = False
    print("⚠️ datasets library not available - using fallback mode")

try:
    from transformers import AutoTokenizer, AutoModel
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("⚠️ transformers library not available - using basic mode")

# Configure logging
logger = logging.getLogger(__name__)

class OdiaGenAIDatasetService:
    """Service to use OdiaGenAI dataset for better Oriya language processing"""
    
    def __init__(self):
        self.hf_token = os.getenv('HUGGINGFACE_API_TOKEN', '')
        self.dataset = None
        self.odia_tokenizer = None
        self.odia_model = None
        self.dataset_loaded = False
        
        # Initialize the service
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize the OdiaGenAI dataset and models"""
        try:
            logger.info("🔄 Initializing OdiaGenAI Dataset Service...")
            
            # Load the OdiaGenAI tokenizer if available
            self._load_odia_tokenizer()
            
            # Load a subset of the dataset for translation reference
            self._load_dataset_subset()
            
            logger.info("✅ OdiaGenAI Dataset Service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize OdiaGenAI Dataset Service: {e}")
    
    def _load_odia_tokenizer(self):
        """Load the OdiaGenAI tokenizer"""
        try:
            # Try to load the OdiaGenAI tokenizer
            tokenizer_name = "shantipriya/OdiaTokenizer"
            self.odia_tokenizer = AutoTokenizer.from_pretrained(
                tokenizer_name,
                token=self.hf_token if self.hf_token else None
            )
            logger.info(f"✅ Loaded OdiaGenAI tokenizer: {tokenizer_name}")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not load OdiaGenAI tokenizer: {e}")
            # Fallback to a multilingual tokenizer
            try:
                self.odia_tokenizer = AutoTokenizer.from_pretrained("bert-base-multilingual-cased")
                logger.info("✅ Loaded fallback multilingual tokenizer")
            except Exception as e2:
                logger.error(f"❌ Could not load any tokenizer: {e2}")
    
    def _load_dataset_subset(self):
        """Load a subset of the OdiaGenAI dataset for reference"""
        try:
            # Load a small subset for translation reference
            logger.info("🔄 Loading OdiaGenAI dataset subset...")
            
            # Try to load the dataset
            self.dataset = load_dataset(
                "OdiaGenAIdata/pre_train_odia_data_processed",
                split="train[:1000]",  # Load first 1000 samples for reference
                token=self.hf_token if self.hf_token else None
            )
            
            self.dataset_loaded = True
            logger.info(f"✅ Loaded {len(self.dataset)} samples from OdiaGenAI dataset")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not load OdiaGenAI dataset: {e}")
            self.dataset_loaded = False
    
    def detect_oriya_content(self, text: str) -> bool:
        """Detect if text contains Oriya content using Unicode ranges"""
        if not text:
            return False
            
        # Oriya Unicode range: U+0B00–U+0B7F
        oriya_pattern = re.compile(r'[\u0B00-\u0B7F]')
        oriya_chars = len(oriya_pattern.findall(text))
        total_chars = len([c for c in text if c.isalpha()])
        
        if total_chars > 0:
            oriya_ratio = oriya_chars / total_chars
            return oriya_ratio > 0.3
        return False
    
    def enhance_oriya_response(self, english_response: str, original_query: str) -> Dict[str, Any]:
        """
        Enhance English response to Oriya using OdiaGenAI dataset context
        """
        try:
            logger.info("🔄 Enhancing response with OdiaGenAI dataset...")
            
            # First, try to use HuggingFace translation models
            oriya_response = self._translate_with_hf_models(english_response)
            
            if oriya_response:
                # Enhance with dataset context if available
                if self.dataset_loaded:
                    enhanced_response = self._enhance_with_dataset_context(
                        oriya_response, original_query
                    )
                    if enhanced_response:
                        oriya_response = enhanced_response
                
                return {
                    'success': True,
                    'oriya_response': oriya_response,
                    'method': 'odiagenai_enhanced',
                    'confidence': 0.85,
                    'original_english': english_response
                }
            
            # Fallback to basic translation
            return self._fallback_translation(english_response)
            
        except Exception as e:
            logger.error(f"❌ Error enhancing Oriya response: {e}")
            return self._fallback_translation(english_response)
    
    def _translate_with_hf_models(self, text: str) -> Optional[str]:
        """Translate using HuggingFace models optimized for Oriya"""
        try:
            # Try multiple Oriya-specific models
            models_to_try = [
                "ai4bharat/indictrans2-en-indic-1B",
                "facebook/nllb-200-distilled-600M",
                "Helsinki-NLP/opus-mt-en-or"
            ]
            
            headers = {
                "Authorization": f"Bearer {self.hf_token}",
                "Content-Type": "application/json"
            }
            
            for model_name in models_to_try:
                try:
                    model_url = f"https://api-inference.huggingface.co/models/{model_name}"
                    
                    # Prepare payload based on model type
                    if "indictrans" in model_name:
                        payload = {
                            "inputs": f"English: {text}\nOdia:",
                            "parameters": {
                                "max_new_tokens": 512,
                                "temperature": 0.3,
                                "do_sample": True
                            }
                        }
                    elif "nllb" in model_name:
                        payload = {
                            "inputs": text,
                            "parameters": {
                                "src_lang": "eng_Latn",
                                "tgt_lang": "ory_Orya"
                            }
                        }
                    else:  # opus-mt
                        payload = {"inputs": text}
                    
                    payload["options"] = {"wait_for_model": True}
                    
                    response = requests.post(model_url, headers=headers, json=payload, timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        if isinstance(result, list) and len(result) > 0:
                            if "translation_text" in result[0]:
                                translation = result[0]["translation_text"].strip()
                                if translation and translation != text:
                                    logger.info(f"✅ Translated using {model_name}")
                                    return translation
                            elif "generated_text" in result[0]:
                                generated = result[0]["generated_text"]
                                # Extract Odia part if it's a completion
                                if "Odia:" in generated:
                                    translation = generated.split("Odia:")[-1].strip()
                                    if translation and translation != text:
                                        logger.info(f"✅ Generated using {model_name}")
                                        return translation
                    
                except Exception as e:
                    logger.warning(f"⚠️ Model {model_name} failed: {e}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"❌ HF translation failed: {e}")
            return None
    
    def _enhance_with_dataset_context(self, oriya_text: str, original_query: str) -> Optional[str]:
        """Enhance translation using dataset context"""
        try:
            if not self.dataset_loaded or not self.dataset:
                return None
            
            # Look for similar content in the dataset
            query_keywords = self._extract_keywords(original_query)
            
            # Find relevant samples from dataset
            relevant_samples = []
            for sample in self.dataset:
                if 'text' in sample:
                    sample_text = sample['text']
                    if any(keyword in sample_text.lower() for keyword in query_keywords):
                        relevant_samples.append(sample_text)
                        if len(relevant_samples) >= 3:  # Limit to 3 samples
                            break
            
            if relevant_samples:
                # Use the context to improve the translation
                logger.info(f"✅ Found {len(relevant_samples)} relevant samples from dataset")
                # For now, return the original translation
                # In a more advanced implementation, you could use the samples to refine the translation
                return oriya_text
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Dataset enhancement failed: {e}")
            return None
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text for dataset matching"""
        # Simple keyword extraction
        financial_keywords = [
            'investment', 'money', 'bank', 'loan', 'savings', 'market', 'finance',
            'ନିବେଶ', 'ଟଙ୍କା', 'ବ୍ୟାଙ୍କ', 'ଋଣ', 'ସଞ୍ଚୟ', 'ବଜାର', 'ଆର୍ଥିକ'
        ]
        
        keywords = []
        text_lower = text.lower()
        
        for keyword in financial_keywords:
            if keyword.lower() in text_lower:
                keywords.append(keyword.lower())
        
        return keywords
    
    def enhance_oriya_response(self, english_response, oriya_query):
        """
        Enhance an English response for Oriya context using the OdiaGenAI dataset
        """
        try:
            print(f"🔄 Enhancing response for Oriya...")
            print(f"📝 English response: {english_response[:100]}...")

            # Check if the response is already in Oriya
            if self.detect_oriya_content(english_response):
                print("✅ Response already contains Oriya content")
                return {
                    'success': True,
                    'oriya_response': english_response,
                    'method': 'already_oriya',
                    'confidence': 0.9
                }

            # **CRITICAL: Translate English response to Oriya**
            print("🔄 Translating English response to Oriya...")
            translation_result = self.translate_to_oriya(english_response, oriya_query)

            if translation_result.get('success') and translation_result.get('oriya_text'):
                oriya_text = translation_result['oriya_text']
                print(f"✅ Translation successful: {oriya_text[:100]}...")
                return {
                    'success': True,
                    'oriya_response': oriya_text,
                    'method': f"odiagenai_{translation_result.get('method', 'enhanced')}",
                    'confidence': translation_result.get('confidence', 0.8)
                }

            # Fallback: Use basic translation
            print("⚠️ Advanced translation failed, using fallback...")
            fallback_result = self._fallback_translation(english_response)

            if fallback_result.get('success'):
                return {
                    'success': True,
                    'oriya_response': fallback_result['oriya_response'],
                    'method': 'odiagenai_fallback',
                    'confidence': 0.5
                }

            # Final fallback: Return original with clear indication
            print("❌ All translation attempts failed")
            return {
                'success': False,
                'oriya_response': f"[Translation Failed - English Response] {english_response}",
                'method': 'enhancement_failed',
                'error': 'All translation methods failed',
                'confidence': 0.1
            }

        except Exception as e:
            print(f"❌ Enhancement error: {e}")
            return {
                'success': False,
                'oriya_response': f"[Enhancement Error] {english_response}",
                'method': 'enhancement_error',
                'error': str(e),
                'confidence': 0.1
            }

    def _fallback_translation(self, text: str) -> Dict[str, Any]:
        """Fallback translation method"""
        print("🔄 Using fallback translation...")

        # Basic word-by-word translation for common terms
        basic_translations = {
            'investment': 'ନିବେଶ',
            'money': 'ଟଙ୍କା',
            'bank': 'ବ୍ୟାଙ୍କ',
            'loan': 'ଋଣ',
            'savings': 'ସଞ୍ଚୟ',
            'market': 'ବଜାର',
            'financial': 'ଆର୍ଥିକ',
            'planning': 'ଯୋଜନା',
            'advice': 'ପରାମର୍ଶ',
            'help': 'ସାହାଯ୍ୟ',
            'portfolio': 'ପୋର୍ଟଫୋଲିଓ',
            'risk': 'ବିପଦ',
            'return': 'ଲାଭ',
            'fund': 'ପାଣ୍ଠି',
            'stock': 'ଷ୍ଟକ୍',
            'bond': 'ବଣ୍ଡ',
            'insurance': 'ବୀମା',
            'tax': 'କର',
            'budget': 'ବଜେଟ୍',
            'income': 'ଆୟ',
            'expense': 'ଖର୍ଚ୍ଚ',
            'profit': 'ଲାଭ',
            'loss': 'କ୍ଷତି'
        }

        translated_text = text
        for english, oriya in basic_translations.items():
            # Case-insensitive replacement
            import re
            pattern = re.compile(re.escape(english), re.IGNORECASE)
            translated_text = pattern.sub(oriya, translated_text)

        # Add Oriya context phrases
        if 'investment' in text.lower() or 'ନିବେଶ' in translated_text:
            translated_text = f"ଆର୍ଥିକ ପରାମର୍ଶ: {translated_text}"

        print(f"✅ Fallback translation completed: {translated_text[:100]}...")

        return {
            'success': True,
            'oriya_response': translated_text,
            'method': 'basic_fallback',
            'confidence': 0.5,
            'original_english': text
        }

# Global instance
odiagenai_dataset_service = OdiaGenAIDatasetService()
