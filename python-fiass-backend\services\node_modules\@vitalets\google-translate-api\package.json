{"name": "@vitalets/google-translate-api", "description": "A free and unlimited API for Google Translate", "version": "9.2.1", "type": "module", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "engines": {"node": ">=14"}, "files": ["src", "dist", "README.md"], "scripts": {"prepare": "git config core.hooksPath ./.hooks", "lint": "eslint src test --ext .ts", "tsc": "tsc --noEmit", "check": "ts-node-esm check.ts", "test": "mocha -n loader=ts-node/esm test/setup.ts test/specs/*.ts", "test:esm": "node test/esm/prepare.mjs && node test/esm/test.cjs && node test/esm/test.mjs", "toc": "markdown-toc README.md -i --maxdepth 4", "build:esm": "rm -rf dist/esm && tsc -p tsconfig.build.json", "build:cjs": "rm -rf dist/cjs && tsc -p tsconfig.build.cjs.json && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json", "build": "npm run build:esm && npm run build:cjs", "release": "npm run lint && npm t && npm run build && npm run test:esm && np --yolo --no-release-draft"}, "lint-staged": {"{src,test}/**/*.ts": "eslint --ext .ts"}, "dependencies": {"@types/http-errors": "^1.8.2", "http-errors": "^2.0.0", "node-fetch": "^2.6.7"}, "devDependencies": {"@types/mocha": "^10.0.0", "@types/node": "^18.11.18", "@types/node-fetch": "^2.6.2", "@typescript-eslint/eslint-plugin": "^5.39.0", "@typescript-eslint/parser": "^5.39.0", "eslint": "^8.25.0", "eslint-plugin-mocha": "^10.1.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "markdown-toc": "^1.2.0", "mocha": "^10.1.0", "np": "^7.7.0", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "repository": {"type": "git", "url": "https://github.com/vitalets/google-translate-api.git"}, "publishConfig": {"access": "public"}, "keywords": ["translate", "translator", "google", "api", "free", "language"], "license": "MIT"}