#!/usr/bin/env python3
"""
Test script to verify chunk_text parsing logic for file name extraction
"""

def test_chunk_text_parsing():
    """Test the chunk_text parsing logic"""
    
    # Sample chunk_text from your data
    sample_chunk_text = 'id: 8129 | file_index: index_demo | file_id: 1745737635763 | page: 528 | total_page: 530 | page_content: Dressing for Altitude: U.S. Aviation Pressure Suits—Wiley Post to Space Shuttle    |    <PERSON>\\n525\\nIndex: Z\\n S1031 Pilots Protective Assembly, 352\\n S1034 Pilots Protective Assembly, 352, 359\\n S1035 Advanced Crew Escape Suit (ACES), 403\\n Sam Browne configuration, 218\\n sealing slide fasteners, 172–73, 443n161\\n U.S. Rubber suits, 45, 50, 61\\n See also lace-adjustment features\\nZoot Suit, 85. See also Cotton Aerodynamic Anti-G Suit\\n<PERSON>wayer, <PERSON>. "Jim," 410 | file_name: dressing-for-altitude-ebook_tagged.pdf | file_extention: pdf'
    
    print("🧪 Testing chunk_text parsing logic...")
    print(f"📄 Sample chunk_text: {sample_chunk_text[:100]}...")
    
    # Initialize variables
    source_title = "Unknown"
    file_id = "Unknown"
    page_number = "Unknown"
    file_extension = "unknown"
    
    # Parse chunk_text to extract file_name, file_id, page, and file_extension
    if sample_chunk_text:
        try:
            # Extract file_name from chunk_text pattern: "file_name: dressing-for-altitude-ebook_tagged.pdf"
            if "file_name:" in sample_chunk_text:
                file_name_start = sample_chunk_text.find("file_name:") + len("file_name:")
                file_name_end = sample_chunk_text.find("|", file_name_start)
                if file_name_end == -1:
                    file_name_end = sample_chunk_text.find(" ", file_name_start)
                if file_name_end != -1:
                    source_title = sample_chunk_text[file_name_start:file_name_end].strip()
                    print(f"   📄 Extracted file_name: {source_title}")

            # Extract file_id from chunk_text pattern: "file_id: 1745737736292"
            if "file_id:" in sample_chunk_text:
                file_id_start = sample_chunk_text.find("file_id:") + len("file_id:")
                file_id_end = sample_chunk_text.find("|", file_id_start)
                if file_id_end == -1:
                    file_id_end = sample_chunk_text.find(" ", file_id_start)
                if file_id_end != -1:
                    file_id = sample_chunk_text[file_id_start:file_id_end].strip()
                    print(f"   🆔 Extracted file_id: {file_id}")

            # Extract page from chunk_text pattern: "page: 528"
            if "page:" in sample_chunk_text:
                page_start = sample_chunk_text.find("page:") + len("page:")
                page_end = sample_chunk_text.find("|", page_start)
                if page_end == -1:
                    page_end = sample_chunk_text.find(" ", page_start)
                if page_end != -1:
                    page_number = sample_chunk_text[page_start:page_end].strip()
                    print(f"   📖 Extracted page: {page_number}")

            # Extract file_extension from chunk_text pattern: "file_extention: pdf"
            if "file_extention:" in sample_chunk_text:
                ext_start = sample_chunk_text.find("file_extention:") + len("file_extention:")
                ext_end = sample_chunk_text.find("|", ext_start)
                if ext_end == -1:
                    ext_end = len(sample_chunk_text)
                file_extension = sample_chunk_text[ext_start:ext_end].strip()
                print(f"   📎 Extracted file_extension: {file_extension}")

        except Exception as e:
            print(f"   ⚠️ Error parsing chunk_text: {str(e)}")
    
    print(f"\n📋 Final extracted data:")
    print(f"   - File Name: {source_title}")
    print(f"   - File ID: {file_id}")
    print(f"   - Page Number: {page_number}")
    print(f"   - File Extension: {file_extension}")
    
    # Determine source type display based on file extension
    if file_extension == "pdf" or (source_title and source_title.endswith('.pdf')):
        source_type_display = 'PDF Document'
    elif file_extension in ["xlsx", "xls", "csv"] or (source_title and any(ext in source_title.lower() for ext in ['.xlsx', '.xls', '.csv'])):
        source_type_display = 'Excel/CSV File'
    elif file_extension in ["doc", "docx"]:
        source_type_display = 'Word Document'
    elif file_extension in ["txt"]:
        source_type_display = 'Text File'
    else:
        source_type_display = 'Document'
    
    print(f"   - Source Type Display: {source_type_display}")
    
    print(f"\n✅ Chunk text parsing test completed successfully!")

if __name__ == "__main__":
    test_chunk_text_parsing()