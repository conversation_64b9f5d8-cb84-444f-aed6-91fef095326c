import React from 'react';
import { PiFilePdf, PiYoutubeLogo, PiGlobe, PiMusicNote } from 'react-icons/pi';
import { UploadType } from './ChatInputUpload';

interface UploadDropdownProps {
  onSelect: (type: UploadType) => void;
  selectedLanguage: string;
  position?: 'above' | 'below';
}

interface UploadOption {
  type: UploadType;
  icon: React.ReactNode;
  label: {
    English: string;
    Tamil: string;
    Telugu: string;
    Kannada: string;
  };
  description: {
    English: string;
    Tamil: string;
    Telugu: string;
    Kannada: string;
  };
}

const uploadOptions: UploadOption[] = [
  {
    type: 'pdf',
    icon: <PiFilePdf className="w-5 h-5" />,
    label: {
      English: 'PDF/Document',
      Tamil: 'PDF/ஆவணம்',
      Telugu: 'PDF/పత్రం',
      Kannada: 'PDF/ದಾಖಲೆ'
    },
    description: {
      English: 'Upload PDF or document files',
      Tamil: 'PDF அல்லது ஆவண கோப்புகளை பதிவேற்றவும்',
      Telugu: 'PDF లేదా పత్రం ఫైల్‌లను అప్‌లోడ్ చేయండి',
      Kannada: 'PDF ಅಥವಾ ದಾಖಲೆ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'
    }
  },
  {
    type: 'youtube',
    icon: <PiYoutubeLogo className="w-5 h-5" />,
    label: {
      English: 'YouTube URL',
      Tamil: 'YouTube URL',
      Telugu: 'YouTube URL',
      Kannada: 'YouTube URL'
    },
    description: {
      English: 'Add YouTube video link',
      Tamil: 'YouTube வீடியோ இணைப்பைச் சேர்க்கவும்',
      Telugu: 'YouTube వీడియో లింక్ జోడించండి',
      Kannada: 'YouTube ವೀಡಿಯೊ ಲಿಂಕ್ ಸೇರಿಸಿ'
    }
  },
  {
    type: 'article',
    icon: <PiGlobe className="w-5 h-5" />,
    label: {
      English: 'Article URL',
      Tamil: 'கட்டுரை URL',
      Telugu: 'వ్యాసం URL',
      Kannada: 'ಲೇಖನ URL'
    },
    description: {
      English: 'Add article or webpage link',
      Tamil: 'கட்டுரை அல்லது வலைப்பக்க இணைப்பைச் சேர்க்கவும்',
      Telugu: 'వ్యాసం లేదా వెబ్‌పేజీ లింక్ జోడించండి',
      Kannada: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ ಲಿಂಕ್ ಸೇರಿಸಿ'
    }
  },
  {
    type: 'mp3',
    icon: <PiMusicNote className="w-5 h-5" />,
    label: {
      English: 'MP3 Audio',
      Tamil: 'MP3 ஆடியோ',
      Telugu: 'MP3 ఆడియో',
      Kannada: 'MP3 ಆಡಿಯೊ'
    },
    description: {
      English: 'Upload audio files',
      Tamil: 'ஆடியோ கோப்புகளை பதிவேற்றவும்',
      Telugu: 'ఆడియో ఫైల్‌లను అప్‌లోడ్ చేయండి',
      Kannada: 'ಆಡಿಯೊ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'
    }
  }
];

const UploadDropdown: React.FC<UploadDropdownProps> = ({ onSelect, selectedLanguage, position = 'above' }) => {
  const getLanguageKey = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return 'Tamil';
      case 'Telugu':
        return 'Telugu';
      case 'Kannada':
        return 'Kannada';
      default:
        return 'English';
    }
  };

  const getHoverColor = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return 'hover:bg-purple-800/30 hover:border-purple-600';
      case 'Telugu':
        return 'hover:bg-green-800/30 hover:border-green-600';
      case 'Kannada':
        return 'hover:bg-orange-800/30 hover:border-orange-600';
      default:
        return 'hover:bg-blue-800/30 hover:border-blue-600';
    }
  };

  const getIconColor = (index: number) => {
    const colors = {
      Tamil: ['text-purple-600', 'text-purple-500', 'text-purple-700', 'text-purple-400'],
      Telugu: ['text-green-600', 'text-green-500', 'text-green-700', 'text-green-400'],
      Kannada: ['text-orange-600', 'text-orange-500', 'text-orange-700', 'text-orange-400'],
      English: ['text-blue-600', 'text-blue-500', 'text-blue-700', 'text-blue-400']
    };

    const languageColors = colors[selectedLanguage as keyof typeof colors] || colors.English;
    return languageColors[index % languageColors.length];
  };

  const languageKey = getLanguageKey();

  return (
    <div className="relative">
      {/* Arrow pointer pointing down to the upload button */}
      <div className="absolute -bottom-2 right-6 w-4 h-4 bg-gray-900 border-r border-b border-gray-700 transform rotate-45 z-10"
           style={{ backgroundColor: 'rgba(17, 24, 39, 0.95)' }}></div>

      <div className="w-72 bg-gray-900 dark:bg-gray-900 rounded-xl border border-gray-700 dark:border-gray-600 shadow-2xl overflow-hidden backdrop-blur-md"
           style={{
             animation: 'slideInDown 0.2s ease-out forwards',
             backgroundColor: 'rgba(17, 24, 39, 0.95)', // Dark semi-transparent
             backdropFilter: 'blur(12px)'
           }}>
        <div className="p-3">
        <div className="text-xs font-semibold text-gray-300 uppercase tracking-wide mb-3 px-1">
          {languageKey === 'Tamil' ? 'பதிவேற்ற வகை தேர்ந்தெடுக்கவும்' :
           languageKey === 'Telugu' ? 'అప్‌లోడ్ రకాన్ని ఎంచుకోండి' :
           languageKey === 'Kannada' ? 'ಅಪ್‌ಲೋಡ್ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆಮಾಡಿ' :
           'Choose Upload Type'}
        </div>
        {uploadOptions.map((option, index) => (
          <button
            key={option.type}
            onClick={() => onSelect(option.type)}
            className={`w-full flex items-start gap-4 p-4 rounded-xl border border-transparent transition-all duration-200 mb-2 last:mb-0 ${getHoverColor()} hover:scale-[1.02] hover:shadow-md`}
          >
            <div className={`flex-shrink-0 p-2 rounded-lg bg-gray-800 ${getIconColor(index)}`}>
              {option.icon}
            </div>
            <div className="flex-grow text-left">
              <div className="font-semibold text-sm text-white mb-1">
                {option.label[languageKey as keyof typeof option.label]}
              </div>
              <div className="text-xs text-gray-300 leading-relaxed">
                {option.description[languageKey as keyof typeof option.description]}
              </div>
            </div>
          </button>
        ))}
        </div>
      </div>
    </div>
  );
};

export default UploadDropdown;

// Add CSS animation styles
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideInDown {
      from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }
  `;
  document.head.appendChild(style);
}
