#!/usr/bin/env python3
"""
Debug script to check what the Node.js translation script is actually returning
and how the Python service is processing it.
"""

import subprocess
import json
import os
import sys

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_translation_response():
    """Debug the translation response processing"""
    
    print("🔍 Debugging Translation Response Processing")
    print("=" * 60)
    
    # Test query
    test_query = "NASA గురించి తెలియజేయండి"  # "Tell me about NASA"
    
    print(f"📝 Test Query: {test_query}")
    print()
    
    # Test 1: Direct Node.js script call
    print("🔬 Test 1: Direct Node.js Script Call")
    print("-" * 40)
    
    script_path = os.path.join(os.path.dirname(__file__), 'services', 'google_translate_script.js')
    
    try:
        result = subprocess.run([
            'node', script_path, 'translate',
            test_query, 'en', 'auto'
        ], capture_output=True, text=True, encoding='utf-8', timeout=30)
        
        print(f"Return Code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0 and result.stdout:
            try:
                response = json.loads(result.stdout)
                print(f"✅ Parsed JSON Response:")
                print(json.dumps(response, indent=2, ensure_ascii=False))
                
                # Check what fields are available
                print(f"\n🔍 Available fields in response:")
                for key, value in response.items():
                    print(f"   {key}: {type(value).__name__} = {value}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON Parse Error: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()
    
    # Test 2: Python Google Translate Service
    print("🔬 Test 2: Python Google Translate Service")
    print("-" * 40)
    
    try:
        from services.google_translate_api_service_new import google_translate_service
        
        if google_translate_service.is_service_available():
            print("✅ Service is available")
            
            # Call the service
            result = google_translate_service.translate_text(test_query, 'en', 'auto')
            
            print(f"🔍 Python Service Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Check what the service is returning
            print(f"\n🔍 Response Analysis:")
            print(f"   Success: {result.get('success', 'N/A')}")
            print(f"   Translated Text: '{result.get('translated_text', 'N/A')}'")
            print(f"   Original Text: '{result.get('original_text', 'N/A')}'")
            print(f"   Source Language: {result.get('source_language', 'N/A')}")
            print(f"   Target Language: {result.get('target_language', 'N/A')}")
            
            # Check if translation actually happened
            original = result.get('original_text', '')
            translated = result.get('translated_text', '')
            
            if original == translated:
                print("⚠️ Translation did not occur - original and translated text are identical")
            else:
                print("✅ Translation occurred - text was changed")
                
        else:
            print("❌ Service not available")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()
    print("🏁 Debug Complete")

if __name__ == "__main__":
    debug_translation_response()
