#!/usr/bin/env python3
"""
Test imports to see if there are any missing dependencies
"""

import sys
import os

def test_import(module_name, description=""):
    try:
        __import__(module_name)
        print(f"✅ {module_name} {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} {description} - Error: {e}")
        return False
    except Exception as e:
        print(f"⚠️ {module_name} {description} - Unexpected error: {e}")
        return False

def main():
    print("🧪 Testing imports for full_code.py")
    print("=" * 50)
    
    # Test basic imports
    imports_to_test = [
        ("os", ""),
        ("csv", ""),
        ("io", ""),
        ("re", ""),
        ("json", ""),
        ("pandas", "as pd"),
        ("numpy", "as np"),
        ("flask", "- Flask framework"),
        ("flask_cors", "- CORS support"),
        ("dotenv", "- Environment variables"),
        ("faiss", "- FAISS vector database"),
        ("langchain_huggingface.embeddings", "- HuggingFace embeddings"),
        ("typing", "- Type hints"),
        ("concurrent.futures", "- Threading"),
        ("threading", ""),
        ("uuid", ""),
        ("time", ""),
        ("datetime", ""),
        ("requests", ""),
        ("openai", "- OpenAI client"),
    ]
    
    success_count = 0
    total_count = len(imports_to_test)
    
    for module, desc in imports_to_test:
        if test_import(module, desc):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"Import Results: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("🎉 All imports successful!")
    else:
        print("⚠️ Some imports failed. Install missing packages.")
    
    # Test if we can import the database module
    print("\n🔍 Testing local modules...")
    sys.path.append('python-fiass-backend')
    test_import("database", "- Local database module")

if __name__ == "__main__":
    main()
