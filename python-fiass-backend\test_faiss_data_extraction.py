#!/usr/bin/env python3
"""
Test script to verify FAISS data extraction for regional languages
"""

import requests
import json

def test_regional_language_query():
    """Test a query in a regional language to verify FAISS data extraction"""
    
    # Test with a Tamil query
    test_data = {
        "query": "நிதி சந்தை பற்றி சொல்லுங்கள்",  # "Tell me about financial markets" in Tamil
        "index_name": "leo"
    }
    
    print("🧪 Testing FAISS data extraction for regional languages...")
    print(f"📝 Query: {test_data['query']}")
    print(f"🔍 Index: {test_data['index_name']}")
    print("-" * 60)
    
    try:
        # Send request to the financial_query endpoint
        response = requests.post(
            "http://127.0.0.1:5010/financial_query",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Request successful!")
            print(f"🌐 Query Language: {data.get('query_language', 'Unknown')}")
            print(f"📊 Search Engine: {data.get('search_engine', 'Unknown')}")
            print(f"📁 Index Used: {data.get('index_used', 'Unknown')}")
            print(f"🔍 Regional Language Detected: {data.get('regional_language_detected', False)}")
            
            # Check sentence analysis for FAISS data
            sentence_analysis = data.get('sentence_analysis', [])
            print(f"\n📋 Sentence Analysis Count: {len(sentence_analysis)}")
            
            if sentence_analysis:
                print("\n🔍 FAISS Data Extraction Results:")
                for i, sentence in enumerate(sentence_analysis[:3]):  # Show first 3 sentences
                    print(f"\n  Sentence {i+1}:")
                    print(f"    📄 Source Title: {sentence.get('source_title', 'N/A')}")
                    print(f"    🔗 Source Type: {sentence.get('source_type', 'N/A')}")
                    print(f"    📁 File ID: {sentence.get('file_id', 'N/A')}")
                    print(f"    📖 Page: {sentence.get('page', 'N/A')}")
                    print(f"    🌐 URL: {sentence.get('url', 'N/A')}")
                    
                    # Check for new FAISS-specific fields
                    page_content = sentence.get('page_content', '')
                    vector_id = sentence.get('vector_id', '')
                    file_uploaded = sentence.get('file_uploaded', '')
                    
                    print(f"    📝 Page Content: {page_content[:100]}..." if page_content else "    📝 Page Content: None")
                    print(f"    🔢 Vector ID: {vector_id}")
                    print(f"    📤 File Uploaded: {file_uploaded}")
                    
                    if page_content or vector_id or file_uploaded:
                        print("    ✅ FAISS-specific fields found!")
                    else:
                        print("    ❌ FAISS-specific fields missing")
            else:
                print("❌ No sentence analysis found")
                
            # Check AI response
            ai_response = data.get('ai_response', '')
            print(f"\n🤖 AI Response Length: {len(ai_response)} characters")
            if ai_response:
                print(f"🤖 AI Response Preview: {ai_response[:200]}...")
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {str(e)}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    test_regional_language_query()
