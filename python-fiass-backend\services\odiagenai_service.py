"""
OdiaGenAI Integration Service
Enhanced Oriya/Odia language translation using OdiaGenAI specialized models
"""

import os
import requests
import json
import time
import logging
from typing import Dict, List, Optional, Tuple
from functools import lru_cache

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OdiaGenAITranslationService:
    """
    Advanced service to integrate with OdiaGenAI models for superior Oriya translation
    Leverages specialized models trained specifically for Odia language tasks
    """
    
    def __init__(self):
        self.base_url = "https://api-inference.huggingface.co/models"
        self.headers = {
            "Authorization": f"Bearer {os.getenv('HUGGINGFACE_API_TOKEN', '')}",
            "Content-Type": "application/json"
        }
        
        # OdiaGenAI models available on Hugging Face
        self.models = {
            "odia_llama2_base": "OdiaGenAI/odia_llama2_7B_base",
            "odia_llama7b_v1": "OdiaGenAI/odiagenAI_llama7b_base_v1",
            "odia_llama2_v1": "OdiaGenAI/odia_llama2_7B_v1",
            "bengali_gpt": "OdiaGenAI/odiagenAI-bengali-base-model-v1",
            "hindi_mistral": "OdiaGenAI/mistral_hindi_7b_base_v1"
        }
        
        # Enhanced translation prompts with better context
        self.translation_prompts = {
            "en_to_or": {
                "prompt": "Translate the following English text to accurate and natural Odia language:\n\nEnglish: {text}\nOdia:",
                "instruction": "Please provide a natural and culturally appropriate Odia translation."
            },
            "or_to_en": {
                "prompt": "Translate the following Odia text to clear and accurate English:\n\nOdia: {text}\nEnglish:",
                "instruction": "Please provide a clear and accurate English translation."
            },
            "hi_to_or": {
                "prompt": "Translate the following Hindi text to natural Odia language:\n\nHindi: {text}\nOdia:",
                "instruction": "Please provide a natural Odia translation maintaining the original meaning."
            },
            "or_to_hi": {
                "prompt": "Translate the following Odia text to clear Hindi:\n\nOdia: {text}\nHindi:",
                "instruction": "Please provide a clear Hindi translation."
            }
        }
        
        # Model performance tracking
        self.model_stats = {
            "total_requests": 0,
            "successful_translations": 0,
            "failed_translations": 0,
            "model_usage": {},
            "avg_response_time": 0.0
        }
    
    def _clean_translation_output(self, generated_text: str, original_prompt: str) -> str:
        """
        Clean and extract the actual translation from model output
        """
        if not generated_text:
            return ""
        
        # Remove the original prompt if it's included in the response
        if original_prompt in generated_text:
            translation = generated_text.replace(original_prompt, "").strip()
        else:
            translation = generated_text.strip()
        
        # Remove common prefixes that models might add
        prefixes_to_remove = [
            "Translation:", "Odia:", "English:", "Hindi:", 
            "ଅନୁବାଦ:", "ଉତ୍ତର:", "Answer:", "Response:"
        ]
        
        for prefix in prefixes_to_remove:
            if translation.startswith(prefix):
                translation = translation[len(prefix):].strip()
        
        # Remove quotes if the entire translation is wrapped in them
        if (translation.startswith('"') and translation.endswith('"')) or \
           (translation.startswith("'") and translation.endswith("'")):
            translation = translation[1:-1].strip()
        
        return translation
    
    def _make_inference_request(
        self, 
        model_name: str, 
        prompt: str, 
        max_retries: int = 3,
        timeout: int = 45
    ) -> Optional[str]:
        """
        Make inference request to Hugging Face API with enhanced error handling
        """
        if not self.headers["Authorization"].replace("Bearer ", ""):
            logger.warning("⚠️ No Hugging Face API token found. Set HUGGINGFACE_API_TOKEN environment variable.")
            return None
            
        model_url = f"{self.base_url}/{model_name}"
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": 256,
                "temperature": 0.3,  # Lower temperature for more consistent translations
                "do_sample": True,
                "top_p": 0.85,
                "repetition_penalty": 1.1,
                "return_full_text": False
            },
            "options": {
                "wait_for_model": True,
                "use_cache": False
            }
        }
        
        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 Making request to {model_name} (attempt {attempt + 1}/{max_retries})")
                
                response = requests.post(
                    model_url, 
                    headers=self.headers, 
                    json=payload, 
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if isinstance(result, list) and len(result) > 0:
                        generated_text = result[0].get("generated_text", "")
                        return self._clean_translation_output(generated_text, prompt)
                    elif isinstance(result, dict) and "generated_text" in result:
                        generated_text = result["generated_text"]
                        return self._clean_translation_output(generated_text, prompt)
                    else:
                        logger.warning(f"Unexpected response format: {result}")
                
                elif response.status_code == 503:
                    logger.info(f"⏳ Model {model_name} is loading, waiting...")
                    time.sleep(15)  # Wait longer for model to load
                    continue
                    
                elif response.status_code == 429:
                    logger.warning("⚠️ Rate limit exceeded, waiting...")
                    time.sleep(30)
                    continue
                    
                else:
                    logger.error(f"❌ API request failed: {response.status_code} - {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ Request timeout (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"🔌 Network error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
        return None
    
    def translate_with_odiagenai(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str,
        model_preference: str = "auto"
    ) -> Dict[str, any]:
        """
        Translate text using OdiaGenAI specialized models
        
        Args:
            text: Text to translate
            source_lang: Source language code (en, or, hi)
            target_lang: Target language code (en, or, hi)
            model_preference: Preferred model ("auto", "odia_llama2_base", "odia_llama7b_v1")
        
        Returns:
            Dictionary with translation results and metadata
        """
        start_time = time.time()
        self.model_stats["total_requests"] += 1
        
        try:
            # Validate input
            if not text or not text.strip():
                return {
                    'success': False,
                    'error': 'Empty text provided',
                    'provider': 'odiagenai',
                    'processing_time': time.time() - start_time
                }
            
            # Determine translation direction
            translation_key = f"{source_lang}_to_{target_lang}"
            
            if translation_key not in self.translation_prompts:
                return {
                    'success': False,
                    'error': f'Translation direction {source_lang} -> {target_lang} not supported by OdiaGenAI',
                    'provider': 'odiagenai',
                    'supported_directions': list(self.translation_prompts.keys()),
                    'processing_time': time.time() - start_time
                }
            
            # Select the best model for the task
            if model_preference == "auto":
                # Use the most reliable model for Odia
                selected_model = self.models["odia_llama2_base"]
                model_key = "odia_llama2_base"
            elif model_preference in self.models:
                selected_model = self.models[model_preference]
                model_key = model_preference
            else:
                selected_model = self.models["odia_llama2_base"]
                model_key = "odia_llama2_base"
            
            # Create enhanced prompt
            prompt_config = self.translation_prompts[translation_key]
            prompt = prompt_config["prompt"].format(text=text.strip())
            
            logger.info(f"🌟 Using OdiaGenAI model: {model_key}")
            
            # Make inference request
            translated_text = self._make_inference_request(selected_model, prompt)
            
            if translated_text and translated_text.strip():
                processing_time = time.time() - start_time
                
                # Update statistics
                self.model_stats["successful_translations"] += 1
                self.model_stats["model_usage"][model_key] = \
                    self.model_stats["model_usage"].get(model_key, 0) + 1
                
                # Update average response time
                total_time = (self.model_stats["avg_response_time"] * 
                            (self.model_stats["successful_translations"] - 1) + processing_time)
                self.model_stats["avg_response_time"] = \
                    total_time / self.model_stats["successful_translations"]
                
                logger.info(f"✅ OdiaGenAI translation successful in {processing_time:.2f}s")
                
                return {
                    'success': True,
                    'translated_text': translated_text.strip(),
                    'provider': 'odiagenai',
                    'model': model_key,
                    'model_full_name': selected_model,
                    'processing_time': processing_time,
                    'confidence': 0.92,  # High confidence for specialized model
                    'translation_direction': translation_key
                }
            else:
                self.model_stats["failed_translations"] += 1
                return {
                    'success': False,
                    'error': 'OdiaGenAI model returned empty or invalid translation',
                    'provider': 'odiagenai',
                    'model': model_key,
                    'processing_time': time.time() - start_time
                }
                
        except Exception as e:
            self.model_stats["failed_translations"] += 1
            logger.error(f"❌ OdiaGenAI translation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'provider': 'odiagenai',
                'processing_time': time.time() - start_time
            }
    
    def is_available(self) -> bool:
        """Check if OdiaGenAI service is available"""
        return bool(self.headers["Authorization"].replace("Bearer ", ""))
    
    def get_supported_directions(self) -> List[str]:
        """Get list of supported translation directions"""
        return list(self.translation_prompts.keys())
    
    def get_available_models(self) -> Dict[str, str]:
        """Get list of available OdiaGenAI models"""
        return self.models.copy()
    
    def get_statistics(self) -> Dict[str, any]:
        """Get service usage statistics"""
        return self.model_stats.copy()
    
    def reset_statistics(self):
        """Reset service statistics"""
        self.model_stats = {
            "total_requests": 0,
            "successful_translations": 0,
            "failed_translations": 0,
            "model_usage": {},
            "avg_response_time": 0.0
        }

# Global instance
odiagenai_translator = OdiaGenAITranslationService()

# Convenience functions
def translate_to_odia_with_odiagenai(text: str, source_lang: str = "en", model: str = "auto") -> Dict[str, any]:
    """
    Convenience function for translating to Odia using OdiaGenAI
    """
    return odiagenai_translator.translate_with_odiagenai(text, source_lang, "or", model)

def translate_from_odia_with_odiagenai(text: str, target_lang: str = "en", model: str = "auto") -> Dict[str, any]:
    """
    Convenience function for translating from Odia using OdiaGenAI
    """
    return odiagenai_translator.translate_with_odiagenai(text, "or", target_lang, model)
