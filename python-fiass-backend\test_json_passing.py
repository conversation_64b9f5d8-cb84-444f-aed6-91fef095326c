import subprocess
import json

# Test data with sentence analysis for reference injection
test_data = {
    "ai_response": "This is a test response. It has multiple sentences. Each sentence should get a reference number.",
    "related_questions": ["How to invest?", "What is risk?"],
    "sentence_analysis": [
        {"sentence": "This is a test response.", "source_title": "Test Source 1"},
        {"sentence": "It has multiple sentences.", "source_title": "Test Source 2"},
        {"sentence": "Each sentence should get a reference number.", "source_title": "Test Source 3"}
    ]
}

data_json = json.dumps(test_data)
print(f"JSON string: {data_json}")

# Test the Node.js script call
result = subprocess.run([
    'node', 'python-fiass-backend/services/google_translate_script.js', 'translate-response',
    data_json, 'ta'
], capture_output=True, text=True, encoding='utf-8', timeout=60)

print(f"Return code: {result.returncode}")
print(f"Stdout: {result.stdout}")
print(f"Stderr: {result.stderr}")
