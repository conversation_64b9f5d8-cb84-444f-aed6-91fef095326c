/**
 * FreightCalculatorWidget - Interactive widget for freight distance calculation
 * Integrates with ChatBox to provide chargeable distance calculations
 */

import React, { useState, useEffect } from 'react';
import { PiCalculator, PiMapPin, PiCurrencyInr, PiWarning, PiCheck, PiInfo } from 'react-icons/pi';

interface Location {
  name: string;
  latitude: number;
  longitude: number;
  stationCode?: string;
}

interface CalculationResult {
  actualDistance: number;
  chargeableDistance: number;
  roundingMethod: string;
  roundingDifference: number;
  tariffClass: string;
  minimumCharge: boolean;
  baseCharge: number;
  taxes: number;
  finalAmount: number;
  breakdown: string[];
  isValid: boolean;
  issues: string[];
  recommendations: string[];
}

interface FreightCalculatorWidgetProps {
  selectedLanguage?: string;
  onCalculationComplete?: (result: CalculationResult) => void;
}

const FreightCalculatorWidget: React.FC<FreightCalculatorWidgetProps> = ({
  selectedLanguage = "English",
  onCalculationComplete
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [origin, setOrigin] = useState<Partial<Location>>({});
  const [destination, setDestination] = useState<Partial<Location>>({});
  const [commodityClass, setCommodityClass] = useState('CLASS_C');
  const [roundingMethod, setRoundingMethod] = useState('nearest_5');
  const [baseRate, setBaseRate] = useState(2.50);
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // Predefined locations for quick selection
  const predefinedLocations: Location[] = [
    { name: "Mumbai Central", latitude: 19.0760, longitude: 72.8777, stationCode: "MMCT" },
    { name: "New Delhi", latitude: 28.6139, longitude: 77.2090, stationCode: "NDLS" },
    { name: "Chennai Central", latitude: 13.0827, longitude: 80.2707, stationCode: "MAS" },
    { name: "Kolkata", latitude: 22.5726, longitude: 88.3639, stationCode: "KOAA" },
    { name: "Bangalore", latitude: 12.9716, longitude: 77.5946, stationCode: "SBC" },
    { name: "Hyderabad", latitude: 17.3850, longitude: 78.4867, stationCode: "HYB" },
    { name: "Pune", latitude: 18.5204, longitude: 73.8567, stationCode: "PUNE" },
    { name: "Ahmedabad", latitude: 23.0225, longitude: 72.5714, stationCode: "ADI" }
  ];

  const commodityClasses = [
    { value: 'CLASS_A', label: 'Class A - High Value Goods', minDistance: 50 },
    { value: 'CLASS_B', label: 'Class B - Medium Value Goods', minDistance: 75 },
    { value: 'CLASS_C', label: 'Class C - Bulk Commodities', minDistance: 100 },
    { value: 'CLASS_D', label: 'Class D - Raw Materials', minDistance: 125 }
  ];

  const roundingMethods = [
    { value: 'nearest_5', label: 'Nearest 5 km' },
    { value: 'nearest_10', label: 'Nearest 10 km' },
    { value: 'ceiling_5', label: 'Ceiling 5 km' },
    { value: 'ceiling_10', label: 'Ceiling 10 km' },
    { value: 'floor_5', label: 'Floor 5 km' },
    { value: 'floor_10', label: 'Floor 10 km' }
  ];

  // Calculate distance using Haversine formula
  const calculateDistance = (loc1: Location, loc2: Location): number => {
    const R = 6371; // Earth's radius in kilometers
    const lat1Rad = (loc1.latitude * Math.PI) / 180;
    const lat2Rad = (loc2.latitude * Math.PI) / 180;
    const deltaLatRad = ((loc2.latitude - loc1.latitude) * Math.PI) / 180;
    const deltaLonRad = ((loc2.longitude - loc1.longitude) * Math.PI) / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  // Calculate chargeable distance with rounding
  const calculateChargeableDistance = (actualDistance: number): number => {
    const minimumDistances = { 'CLASS_A': 50, 'CLASS_B': 75, 'CLASS_C': 100, 'CLASS_D': 125 };
    const minDistance = minimumDistances[commodityClass as keyof typeof minimumDistances] || 100;
    const baseDistance = Math.max(actualDistance, minDistance);

    switch (roundingMethod) {
      case 'nearest_5': return Math.round(baseDistance / 5) * 5;
      case 'nearest_10': return Math.round(baseDistance / 10) * 10;
      case 'ceiling_5': return Math.ceil(baseDistance / 5) * 5;
      case 'ceiling_10': return Math.ceil(baseDistance / 10) * 10;
      case 'floor_5': return Math.floor(baseDistance / 5) * 5;
      case 'floor_10': return Math.floor(baseDistance / 10) * 10;
      default: return Math.round(baseDistance / 5) * 5;
    }
  };

  // Perform calculation
  const handleCalculate = async () => {
    if (!origin.latitude || !origin.longitude || !destination.latitude || !destination.longitude) {
      return;
    }

    setIsCalculating(true);

    try {
      const actualDistance = calculateDistance(origin as Location, destination as Location);
      const chargeableDistance = calculateChargeableDistance(actualDistance);
      const roundingDifference = chargeableDistance - actualDistance;
      
      const minimumDistances = { 'CLASS_A': 50, 'CLASS_B': 75, 'CLASS_C': 100, 'CLASS_D': 125 };
      const minDistance = minimumDistances[commodityClass as keyof typeof minimumDistances] || 100;
      const minimumCharge = actualDistance < minDistance;

      const baseCharge = chargeableDistance * baseRate;
      const taxes = baseCharge * 0.18; // 18% GST
      const finalAmount = baseCharge + taxes;

      const breakdown = [
        `Actual Distance: ${actualDistance.toFixed(2)} km`,
        `Chargeable Distance: ${chargeableDistance} km (${roundingMethod})`,
        `Rounding Difference: ${roundingDifference.toFixed(2)} km`,
        `Base Rate: ₹${baseRate}/km`,
        `Base Charge: ₹${baseCharge.toFixed(2)}`,
        `GST (18%): ₹${taxes.toFixed(2)}`,
        `Total Amount: ₹${finalAmount.toFixed(2)}`
      ];

      if (minimumCharge) {
        breakdown.splice(1, 0, `Minimum Distance Applied: ${minDistance} km`);
      }

      // Validation
      const issues: string[] = [];
      const recommendations: string[] = [];
      
      const roundingPercentage = Math.abs(roundingDifference) / actualDistance * 100;
      if (roundingPercentage > 10) {
        issues.push(`High rounding difference: ${roundingPercentage.toFixed(1)}%`);
        recommendations.push('Consider using a different rounding method');
      }

      if (minimumCharge) {
        recommendations.push('Minimum distance charge applied - ensure customer is informed');
      }

      const calculationResult: CalculationResult = {
        actualDistance,
        chargeableDistance,
        roundingMethod,
        roundingDifference,
        tariffClass: commodityClass,
        minimumCharge,
        baseCharge,
        taxes,
        finalAmount,
        breakdown,
        isValid: issues.length === 0,
        issues,
        recommendations
      };

      setResult(calculationResult);
      onCalculationComplete?.(calculationResult);

    } catch (error) {
      console.error('Calculation error:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  // Get text based on selected language
  const getText = (key: string): string => {
    const texts: Record<string, Record<string, string>> = {
      English: {
        title: "Freight Distance Calculator",
        origin: "Origin Station",
        destination: "Destination Station",
        commodityClass: "Commodity Class",
        roundingMethod: "Rounding Method",
        baseRate: "Base Rate (₹/km)",
        calculate: "Calculate Freight",
        calculating: "Calculating...",
        results: "Calculation Results",
        validation: "Validation",
        recommendations: "Recommendations",
        close: "Close"
      },
      Tamil: {
        title: "சரக்கு தூர கணக்கீட்டாளர்",
        origin: "தொடக்க நிலையம்",
        destination: "இலக்கு நிலையம்",
        commodityClass: "பொருள் வகுப்பு",
        roundingMethod: "ரவுண்டிங் முறை",
        baseRate: "அடிப்படை விலை (₹/கிமீ)",
        calculate: "சரக்கு கணக்கிடு",
        calculating: "கணக்கிடுகிறது...",
        results: "கணக்கீட்டு முடிவுகள்",
        validation: "சரிபார்ப்பு",
        recommendations: "பரிந்துரைகள்",
        close: "மூடு"
      }
    };

    return texts[selectedLanguage]?.[key] || texts.English[key] || key;
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all
          ${selectedLanguage === "Tamil"
            ? 'border-purple-300 text-purple-600 hover:bg-purple-50'
            : 'border-blue-300 text-blue-600 hover:bg-blue-50'
          }`}
        title={getText('title')}
      >
        <PiCalculator className="w-4 h-4" />
        <span className="text-sm font-medium">{getText('title')}</span>
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
              <PiCalculator className="w-6 h-6" />
              {getText('title')}
            </h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ×
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Input Section */}
            <div className="space-y-4">
              {/* Origin */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <PiMapPin className="inline w-4 h-4 mr-1" />
                  {getText('origin')}
                </label>
                <select
                  value={origin.name || ''}
                  onChange={(e) => {
                    const selected = predefinedLocations.find(loc => loc.name === e.target.value);
                    if (selected) setOrigin(selected);
                  }}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select origin station</option>
                  {predefinedLocations.map(loc => (
                    <option key={loc.stationCode} value={loc.name}>
                      {loc.name} ({loc.stationCode})
                    </option>
                  ))}
                </select>
              </div>

              {/* Destination */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <PiMapPin className="inline w-4 h-4 mr-1" />
                  {getText('destination')}
                </label>
                <select
                  value={destination.name || ''}
                  onChange={(e) => {
                    const selected = predefinedLocations.find(loc => loc.name === e.target.value);
                    if (selected) setDestination(selected);
                  }}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select destination station</option>
                  {predefinedLocations.map(loc => (
                    <option key={loc.stationCode} value={loc.name}>
                      {loc.name} ({loc.stationCode})
                    </option>
                  ))}
                </select>
              </div>

              {/* Commodity Class */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getText('commodityClass')}
                </label>
                <select
                  value={commodityClass}
                  onChange={(e) => setCommodityClass(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {commodityClasses.map(cls => (
                    <option key={cls.value} value={cls.value}>
                      {cls.label} (Min: {cls.minDistance}km)
                    </option>
                  ))}
                </select>
              </div>

              {/* Rounding Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getText('roundingMethod')}
                </label>
                <select
                  value={roundingMethod}
                  onChange={(e) => setRoundingMethod(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {roundingMethods.map(method => (
                    <option key={method.value} value={method.value}>
                      {method.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Base Rate */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <PiCurrencyInr className="inline w-4 h-4 mr-1" />
                  {getText('baseRate')}
                </label>
                <input
                  type="number"
                  value={baseRate}
                  onChange={(e) => setBaseRate(parseFloat(e.target.value) || 0)}
                  step="0.01"
                  min="0"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Calculate Button */}
              <button
                onClick={handleCalculate}
                disabled={!origin.latitude || !destination.latitude || isCalculating}
                className={`w-full py-3 px-4 rounded-md font-medium transition-all
                  ${!origin.latitude || !destination.latitude || isCalculating
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : selectedLanguage === "Tamil"
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
              >
                {isCalculating ? getText('calculating') : getText('calculate')}
              </button>
            </div>

            {/* Results Section */}
            <div className="space-y-4">
              {result && (
                <>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                      <PiInfo className="w-5 h-5" />
                      {getText('results')}
                    </h3>
                    <div className="space-y-2 text-sm">
                      {result.breakdown.map((line, index) => (
                        <div key={index} className="flex justify-between">
                          <span>{line.split(':')[0]}:</span>
                          <span className="font-medium">{line.split(':')[1]}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Validation */}
                  <div className={`p-4 rounded-lg ${result.isValid ? 'bg-green-50' : 'bg-yellow-50'}`}>
                    <h4 className="font-semibold flex items-center gap-2 mb-2">
                      {result.isValid ? (
                        <PiCheck className="w-5 h-5 text-green-600" />
                      ) : (
                        <PiWarning className="w-5 h-5 text-yellow-600" />
                      )}
                      {getText('validation')}
                    </h4>
                    
                    {result.issues.length > 0 && (
                      <div className="text-sm text-yellow-700 mb-2">
                        <strong>Issues:</strong>
                        <ul className="list-disc list-inside ml-2">
                          {result.issues.map((issue, index) => (
                            <li key={index}>{issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {result.recommendations.length > 0 && (
                      <div className="text-sm text-blue-700">
                        <strong>{getText('recommendations')}:</strong>
                        <ul className="list-disc list-inside ml-2">
                          {result.recommendations.map((rec, index) => (
                            <li key={index}>{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FreightCalculatorWidget;