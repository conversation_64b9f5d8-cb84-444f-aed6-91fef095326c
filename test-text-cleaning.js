/**
 * Test script to demonstrate the TextCleaningService functionality
 * Run this with: node test-text-cleaning.js
 */

// Mock the TextCleaningService for testing (since we can't import TypeScript directly)
class TextCleaningService {
  static cleanRepetitiveText(text) {
    if (!text || text.trim().length === 0) {
      return {
        hasRepetition: false,
        repetitionRatio: 0,
        repeatedWords: [],
        cleanedText: text,
        originalLength: 0,
        cleanedLength: 0
      };
    }

    const originalLength = text.length;
    
    // Split text into sentences for better processing
    const sentences = text.split(/[.!?।]+/).filter(s => s.trim().length > 0);
    const cleanedSentences = [];

    let totalRepeatedWords = [];

    for (const sentence of sentences) {
      const cleanedSentence = this.cleanSentenceRepetition(sentence.trim());
      cleanedSentences.push(cleanedSentence.cleaned);
      totalRepeatedWords.push(...cleanedSentence.repeatedWords);
    }

    const cleanedText = cleanedSentences.join('. ').trim();
    const cleanedLength = cleanedText.length;
    
    // Calculate repetition ratio
    const repetitionRatio = totalRepeatedWords.length > 0 ? 
      (originalLength - cleanedLength) / originalLength : 0;

    return {
      hasRepetition: totalRepeatedWords.length > 0,
      repetitionRatio,
      repeatedWords: [...new Set(totalRepeatedWords)], // Remove duplicates
      cleanedText: cleanedText + (cleanedText.endsWith('.') ? '' : '.'),
      originalLength,
      cleanedLength
    };
  }

  static cleanSentenceRepetition(sentence) {
    if (!sentence || sentence.trim().length === 0) {
      return { cleaned: sentence, repeatedWords: [] };
    }

    // Split into words, preserving punctuation
    const words = sentence.split(/\s+/).filter(word => word.trim().length > 0);
    const cleanedWords = [];
    const repeatedWords = [];
    
    // Track word frequency in a sliding window
    const windowSize = 5; // Look for repetition in the last 5 words
    
    for (let i = 0; i < words.length; i++) {
      const currentWord = words[i].toLowerCase().replace(/[^\w\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/g, ''); // Keep Tamil, Telugu, Kannada, Oriya characters
      
      if (currentWord.length === 0) {
        cleanedWords.push(words[i]);
        continue;
      }

      // Check if this word appears in the recent window
      let isRepetitive = false;
      const startIndex = Math.max(0, cleanedWords.length - windowSize);
      
      for (let j = startIndex; j < cleanedWords.length; j++) {
        const previousWord = cleanedWords[j].toLowerCase().replace(/[^\w\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/g, '');
        
        if (previousWord === currentWord && currentWord.length > 2) { // Only consider words longer than 2 characters
          isRepetitive = true;
          repeatedWords.push(currentWord);
          break;
        }
      }

      // Add word only if it's not repetitive
      if (!isRepetitive) {
        cleanedWords.push(words[i]);
      }
    }

    return {
      cleaned: cleanedWords.join(' '),
      repeatedWords
    };
  }

  static quickClean(text) {
    if (!text || text.trim().length === 0) {
      return text;
    }

    const result = this.cleanRepetitiveText(text);
    return result.cleanedText;
  }
}

// Test cases
console.log('🧹 Testing TextCleaningService for repetitive word removal\n');

const testCases = [
  {
    name: "English repetitive words",
    text: "The market market market is going up up up and the stocks stocks are performing well well well."
  },
  {
    name: "Speech-to-text repetition",
    text: "I want to know know know about the financial financial report report for this this quarter quarter."
  },
  {
    name: "Mixed repetition",
    text: "Please tell tell me about about the investment investment options options available available in the the market market today today."
  },
  {
    name: "No repetition",
    text: "What are the best investment strategies for long-term growth?"
  },
  {
    name: "Tamil text with repetition",
    text: "நிதி நிதி அறிக்கை அறிக்கை பற்றி பற்றி சொல்லுங்கள் சொல்லுங்கள்."
  },
  {
    name: "Short repetitive words (should be ignored)",
    text: "I am am going to to the the bank."
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}`);
  console.log(`Original: "${testCase.text}"`);
  
  const result = TextCleaningService.cleanRepetitiveText(testCase.text);
  
  console.log(`Cleaned:  "${result.cleanedText}"`);
  console.log(`Has repetition: ${result.hasRepetition}`);
  console.log(`Repetition ratio: ${(result.repetitionRatio * 100).toFixed(1)}%`);
  console.log(`Repeated words: [${result.repeatedWords.join(', ')}]`);
  console.log(`Length reduction: ${result.originalLength - result.cleanedLength} characters`);
  console.log('---');
});

// Test the quick clean method
console.log('\n🚀 Testing quickClean method:');
const quickTestText = "The the the market is is going going up up and and stocks stocks are are performing performing well well well.";
console.log(`Original: "${quickTestText}"`);
console.log(`Quick cleaned: "${TextCleaningService.quickClean(quickTestText)}"`);

console.log('\n✅ Text cleaning tests completed!');
console.log('\n📝 Integration points in ChatBox:');
console.log('1. Real-time cleaning during speech-to-text input');
console.log('2. Manual cleaning button for typed input');
console.log('3. Cleaning button for voice transcripts');
console.log('4. Automatic cleaning before sending to API');
console.log('5. Cleaning of translated responses');