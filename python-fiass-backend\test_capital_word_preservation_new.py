#!/usr/bin/env python3
"""
Test script for the new capital word preservation system in Google Translate API service.
This tests the improved system that preserves capital words without using CAPWORD tokens.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.google_translate_api_service_new import google_translate_service

def test_capital_word_preservation():
    """Test the new capital word preservation system"""
    
    print("🧪 TESTING NEW CAPITAL WORD PRESERVATION SYSTEM")
    print("=" * 60)
    
    # Test cases with various capital letter patterns
    test_cases = [
        {
            "text": "NASA launched a new satellite using advanced API technology",
            "target_lang": "ta",  # Tamil
            "description": "Acronyms (NASA, API)",
            "expected_preserved": ["NASA", "API"]
        },
        {
            "text": "The iPhone uses iOS operating system with REST APIs",
            "target_lang": "te",  # Telugu
            "description": "Mixed case (iPhone, iOS) + Acronyms (REST, APIs)",
            "expected_preserved": ["iPhone", "iOS", "REST", "APIs"]
        },
        {
            "text": "Microsoft Azure provides cloud services with JSON data format",
            "target_lang": "kn",  # Kannada
            "description": "Proper nouns (Microsoft, Azure) + Acronym (JSON)",
            "expected_preserved": ["Microsoft", "Azure", "JSON"]
        },
        {
            "text": "GitHub and GitLab use HTTP and HTTPS protocols",
            "target_lang": "or",  # Oriya
            "description": "Mixed case (GitHub, GitLab) + Acronyms (HTTP, HTTPS)",
            "expected_preserved": ["GitHub", "GitLab", "HTTP", "HTTPS"]
        },
        {
            "text": "The WWII aircraft used NACA airfoils and XFV design from OWRA",
            "target_lang": "hi",  # Hindi
            "description": "Multiple acronyms (WWII, NACA, XFV, OWRA)",
            "expected_preserved": ["WWII", "NACA", "XFV", "OWRA"]
        }
    ]
    
    print(f"Running {len(test_cases)} test cases...\n")
    
    # Check if service is available
    if not google_translate_service.is_service_available():
        print("❌ Google Translate API service is not available")
        print("   Make sure Node.js and npm are installed")
        print("   The service will install @vitalets/google-translate-api automatically")
        return False
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['description']}")
        print(f"Original: '{test_case['text']}'")
        print(f"Target Language: {test_case['target_lang']}")
        print(f"Expected Preserved Words: {test_case['expected_preserved']}")
        
        try:
            # Test the translation with capital word preservation
            result = google_translate_service.translate_text(
                test_case['text'], 
                test_case['target_lang']
            )
            
            if result.get('success'):
                translated_text = result.get('translatedText', '')
                preserved_words = result.get('capitalWordsPreserved', [])
                
                print(f"Translated: '{translated_text}'")
                print(f"Preserved Words: {preserved_words}")
                
                # Check if capital words are preserved in the translation
                missing_words = []
                for word in test_case['expected_preserved']:
                    if word not in translated_text:
                        missing_words.append(word)
                
                if missing_words:
                    print(f"❌ FAIL - Missing capital words: {missing_words}")
                    all_passed = False
                else:
                    print("✅ PASS - All capital words preserved in translation")
                
                # Check that no CAPWORD tokens appear in the result
                if 'CAPWORD' in translated_text:
                    print("❌ FAIL - CAPWORD tokens found in translation (should not happen)")
                    all_passed = False
                else:
                    print("✅ PASS - No CAPWORD tokens in translation")
                    
            else:
                error = result.get('error', 'Unknown error')
                print(f"❌ FAIL - Translation failed: {error}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            all_passed = False
        
        print("-" * 50)
        print()
    
    return all_passed

def test_financial_response_preservation():
    """Test capital word preservation in financial response translation"""
    
    print("🧪 TESTING FINANCIAL RESPONSE CAPITAL WORD PRESERVATION")
    print("=" * 60)
    
    # Mock financial response data
    response_data = {
        "ai_response": "NASA's budget allocation for SpaceX missions includes API integration costs. The HTTP protocols ensure secure JSON data transfer between GitHub repositories.",
        "related_questions": [
            "What is NASA's partnership with SpaceX?",
            "How does API integration work with JSON?",
            "What are HTTP and HTTPS protocols?"
        ]
    }
    
    target_lang = "ta"  # Tamil
    
    print(f"Testing financial response translation to {target_lang}")
    print(f"Original AI Response: {response_data['ai_response']}")
    print(f"Original Questions: {response_data['related_questions']}")
    
    try:
        result = google_translate_service.translate_financial_response(response_data, target_lang)
        
        if result.get('success'):
            translated_data = result.get('data', {})
            
            print(f"\nTranslated AI Response: {translated_data.get('ai_response', 'N/A')}")
            print(f"Translated Questions: {translated_data.get('related_questions', [])}")
            
            # Check for capital word preservation
            ai_response = translated_data.get('ai_response', '')
            expected_words = ['NASA', 'SpaceX', 'API', 'HTTP', 'JSON', 'GitHub']
            
            missing_words = [word for word in expected_words if word not in ai_response]
            
            if missing_words:
                print(f"❌ Missing capital words in AI response: {missing_words}")
                return False
            else:
                print("✅ All capital words preserved in AI response")
            
            # Check that no CAPWORD tokens appear
            if 'CAPWORD' in ai_response:
                print("❌ CAPWORD tokens found in AI response")
                return False
            else:
                print("✅ No CAPWORD tokens in AI response")
            
            return True
            
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ Financial response translation failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Capital Word Preservation Tests")
    print("=" * 60)
    
    # Test individual text translation
    test1_passed = test_capital_word_preservation()
    
    print("\n" + "=" * 60)
    
    # Test financial response translation
    test2_passed = test_financial_response_preservation()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED - Capital word preservation is working correctly!")
        print("🎉 No CAPWORD tokens are being used - capital words are preserved naturally")
    else:
        print("❌ SOME TESTS FAILED - Capital word preservation needs improvement")
        
    print("\n🔍 Key Features Tested:")
    print("   ✓ Acronym preservation (NASA, API, HTTP, etc.)")
    print("   ✓ Mixed-case word preservation (iPhone, SpaceX, GitHub)")
    print("   ✓ No CAPWORD token corruption")
    print("   ✓ Financial response translation")
    print("   ✓ Related questions translation")
