#!/usr/bin/env python3
"""
Comprehensive test script to verify Tamil processing fixes
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

from full_code import (
    clean_multilingual_response, 
    clean_tamil_text_advanced, 
    clean_tamil_sentence,
    clean_tamil_related_questions,
    detect_word_repetition
)

def test_tamil_text_cleaning():
    """Test Tamil text cleaning functions"""
    
    print("🧪 Testing Tamil Text Cleaning Functions")
    print("=" * 60)
    
    # Test cases with problematic Tamil text
    test_cases = [
        {
            "name": "Word Repetition",
            "text": "IOCL SCC யூனியன் scc இந்தியா (ரயில்வே) மற்றும் இந்தியன் ஆயில் கார்ப்பரேஷன் லிமிடெட் (ஐ. ஓ. சி. எல்) iocl ஆகியவற்றுக்கு இடையேயான சரக்கு கட்டண தகராறின் உண்மை உண்மை முரண்பாடு முரண்பாடு சரக்கு சரக்கு ஒரு ஒரு \"அதிக\" அல்லது \"சட்டவிரோத\" சுற்றியுள்ளன."
        },
        {
            "name": "Clean Tamil Text",
            "text": "இந்திய ஒன்றியம் (ரயில்வே) மற்றும் இந்தியன் ஆயில் கார்ப்பரேஷன் லிமிடெட் இடையேயான சரக்கு கட்டண தகராறில் உள்ள முக்கிய உண்மை முரண்பாடு என்ன?"
        },
        {
            "name": "Mixed Language with Repetition",
            "text": "நிதி நிதி அறிக்கை அறிக்கை financial financial report report தமிழ் தமிழ் language language"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Original: {test_case['text']}")
        
        # Test word repetition detection
        word_corrupted, word_cleaned = detect_word_repetition(test_case['text'])
        print(f"   Word Repetition Detected: {word_corrupted}")
        print(f"   Word Cleaned: {word_cleaned}")
        
        # Test advanced Tamil cleaning
        advanced_cleaned = clean_tamil_text_advanced(test_case['text'])
        print(f"   Advanced Cleaned: {advanced_cleaned}")
        
        # Test sentence cleaning
        sentence_cleaned = clean_tamil_sentence(test_case['text'])
        print(f"   Sentence Cleaned: {sentence_cleaned}")
        print()

def test_tamil_related_questions():
    """Test Tamil related questions cleaning"""
    
    print("🧪 Testing Tamil Related Questions Cleaning")
    print("=" * 60)
    
    # Test questions with repetition
    test_questions = [
        "இந்த இந்த நிதி நிதி திட்டம் திட்டம் எப்போது எப்போது தொடங்கும் தொடங்கும்?",
        "சரக்கு கட்டண விகிதம் எவ்வளவு?",
        "அரசு அரசு கொள்கை கொள்கை மாற்றம் மாற்றம் எப்போது எப்போது நடைமுறைக்கு நடைமுறைக்கு வரும் வரும்?",
        "நிதி அமைச்சர் என்ன அறிவித்தார்?",
        "பங்குச் பங்குச் சந்தை சந்தை நிலவரம் நிலவரம் எப்படி எப்படி உள்ளது உள்ளது?"
    ]
    
    print(f"Original Questions ({len(test_questions)}):")
    for i, q in enumerate(test_questions, 1):
        print(f"   {i}. {q}")
    
    # Clean the questions
    cleaned_questions = clean_tamil_related_questions(test_questions)
    
    print(f"\nCleaned Questions ({len(cleaned_questions)}):")
    for i, q in enumerate(cleaned_questions, 1):
        print(f"   {i}. {q}")

def test_multilingual_response_cleaning():
    """Test multilingual response cleaning"""
    
    print("🧪 Testing Multilingual Response Cleaning")
    print("=" * 60)
    
    # Mock response data with Tamil content
    response_data = {
        "ai_response": "IOCL SCC யூனியன் scc இந்தியா (ரயில்வே) மற்றும் இந்தியன் ஆயில் கார்ப்பரேஷன் லிமிடெட் (ஐ. ஓ. சி. எல்) iocl ஆகியவற்றுக்கு இடையேயான சரக்கு கட்டண தகராறின் உண்மை உண்மை முரண்பாடு முரண்பாடு சரக்கு சரக்கு ஒரு ஒரு \"அதிக\" அல்லது \"சட்டவிரோத\" சுற்றியுள்ளன.",
        "related_questions": [
            "இந்த இந்த நிதி நிதி திட்டம் திட்டம் எப்போது எப்போது தொடங்கும் தொடங்கும்?",
            "சரக்கு கட்டண விகிதம் எவ்வளவு?",
            "அரசு அரசு கொள்கை கொள்கை மாற்றம் மாற்றம் எப்போது எப்போது நடைமுறைக்கு நடைமுறைக்கு வரும் வரும்?"
        ],
        "summary": "நிதி நிதி அறிக்கை அறிக்கை financial financial report report"
    }
    
    print("Original Response Data:")
    print(f"   AI Response: {response_data['ai_response']}")
    print(f"   Related Questions: {response_data['related_questions']}")
    print(f"   Summary: {response_data['summary']}")
    
    # Clean the response
    cleaned_response = clean_multilingual_response(response_data, "Tamil")
    
    print("\nCleaned Response Data:")
    print(f"   AI Response: {cleaned_response['ai_response']}")
    print(f"   Related Questions: {cleaned_response['related_questions']}")
    print(f"   Summary: {cleaned_response.get('summary', 'N/A')}")
    
    # Check for cleaning metadata
    if 'cleaning_metadata' in cleaned_response:
        print(f"   Cleaning Metadata: {cleaned_response['cleaning_metadata']}")

def main():
    """Run all tests"""
    
    print("🚀 Starting Comprehensive Tamil Processing Tests")
    print("=" * 80)
    
    try:
        test_tamil_text_cleaning()
        print("\n" + "=" * 80)
        test_tamil_related_questions()
        print("\n" + "=" * 80)
        test_multilingual_response_cleaning()
        
        print("\n" + "=" * 80)
        print("✅ All tests completed successfully!")
        print("🎉 Tamil processing fixes are working correctly!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()