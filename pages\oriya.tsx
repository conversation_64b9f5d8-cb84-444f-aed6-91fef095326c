import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiTranslate, PiGlobe } from 'react-icons/pi';
import Header from '@/components/Header';

type Language = 'english' | 'tamil' | 'telugu' | 'kannada' | 'oriya';

interface Labels {
  reference: string;
  source: string;
  visit: string;
  back: string;
  translate: string;
  translating: string;
  originalText: string;
  translatedText: string;
  fetchingContent: string;
  translatingWebpage: string;
  webpageContent: string;
  errorFetching: string;
}

const getLabels = (language: Language): Labels => {
  switch (language) {
    case 'oriya':
      return {
        reference: 'ସନ୍ଦର୍ଭ',
        source: 'ମୂଳ ଲିଙ୍କ',
        visit: 'ଦେଖନ୍ତୁ',
        back: 'ଫେରିଯାଅ',
        translate: 'ଅନୁବାଦ',
        translating: 'ଅନୁବାଦ କରୁଛି...',
        originalText: 'ମୂଳ ପାଠ',
        translatedText: 'ଅନୁବାଦିତ ପାଠ',
        fetchingContent: 'ବିଷୟବସ୍ତୁ ଆଣୁଛି...',
        translatingWebpage: 'ୱେବପେଜ୍ ଅନୁବାଦ କରୁଛି...',
        webpageContent: 'ୱେବପେଜ୍ ବିଷୟବସ୍ତୁ',
        errorFetching: 'ବିଷୟବସ୍ତୁ ଆଣିବାରେ ତ୍ରୁଟି'
      };
    default:
      return {
        reference: 'Reference',
        source: 'Source Link',
        visit: 'Visit',
        back: 'Back',
        translate: 'Translate',
        translating: 'Translating...',
        originalText: 'Original Text',
        translatedText: 'Translated Text',
        fetchingContent: 'Fetching content...',
        translatingWebpage: 'Translating webpage...',
        webpageContent: 'Webpage Content',
        errorFetching: 'Error fetching content'
      };
  }
};

const OriyaPage: React.FC = () => {
  const router = useRouter();
  const { url, domain, referenceNumber, returnUrl } = router.query;
  const [isTranslating, setIsTranslating] = useState(false);
  const [translatedContent, setTranslatedContent] = useState<string | null>(null);
  const [pageContent, setPageContent] = useState<string | null>(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [isFetchingWebpage, setIsFetchingWebpage] = useState(false);
  const [webpageContent, setWebpageContent] = useState<string | null>(null);
  const [translatedWebpageContent, setTranslatedWebpageContent] = useState<string | null>(null);

  const labels = getLabels('oriya');

  // Function to extract text content from HTML
  const extractTextFromHTML = (html: string): string => {
    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Remove script and style elements
    const scripts = tempDiv.querySelectorAll('script, style');
    scripts.forEach(script => script.remove());

    // Get text content and clean it up
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Clean up the text: remove extra whitespace, empty lines
    return textContent
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
  };

  // Function to translate text using optimized translation approach
  const translateTextToOriya = async (text: string): Promise<string> => {
    try {
      console.log('Translating text to Oriya:', text.substring(0, 100) + '...');

      // Optimize chunk size for better performance
      const maxChunkSize = 1000; // Increased chunk size for fewer API calls
      if (text.length <= maxChunkSize) {
        return await translateSingleChunkToOriya(text);
      } else {
        // Split into sentences first, then group into chunks to maintain context
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';

        for (const sentence of sentences) {
          if ((currentChunk + sentence).length > maxChunkSize && currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
            currentChunk = sentence;
          } else {
            currentChunk += (currentChunk ? '. ' : '') + sentence;
          }
        }
        if (currentChunk.trim().length > 0) {
          chunks.push(currentChunk.trim());
        }

        // Translate chunks in parallel for better performance
        const translationPromises = chunks.map(async (chunk, index) => {
          // Add small staggered delay to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, index * 100));
          return await translateSingleChunkToOriya(chunk);
        });

        const translatedChunks = await Promise.all(translationPromises);
        return translatedChunks.join(' ');
      }
    } catch (error) {
      console.error('Translation error:', error);
      // Fallback: return original text with a prefix indicating translation attempt
      return `[ଅନୁବାଦ ଚେଷ୍ଟା] ${text}`;
    }
  };

  // Helper function to translate a single chunk with optimized service selection
  const translateSingleChunkToOriya = async (text: string): Promise<string> => {
    const translationServices = [
      // Service 1: Improved Oriya Translation (multiple methods) - highest priority
      async () => {
        console.log('🌟 Trying Improved Oriya Translation Service');
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 45000); // 45 second timeout

        try {
          const response = await fetch('http://localhost:5010/translate-to-oriya', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text: text,
              method: 'auto' // Will try odiagenai, google, mymemory, dictionary in order
            }),
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) throw new Error(`Improved Oriya API error: ${response.status}`);
          const result = await response.json();

          if (result.success && result.data && result.data.translated_text) {
            const translatedText = result.data.translated_text;
            const method = result.data.method || 'unknown';
            const confidence = result.data.confidence || 0.7;

            console.log(`✅ Improved Oriya translation successful using ${method} (confidence: ${confidence})`);

            // Add method indicator to the translation
            const methodIndicator = method.includes('odiagenai') ? '[OdiaGenAI]' :
                                  method.includes('google') ? '[Google]' :
                                  method.includes('mymemory') ? '[MyMemory]' :
                                  method.includes('dictionary') ? '[ଅନୁବାଦ]' : '[Translated]';

            return `${methodIndicator} ${translatedText}`;
          }
          throw new Error('Improved Oriya API returned invalid response');
        } catch (error) {
          clearTimeout(timeoutId);
          console.log('⚠️ Improved Oriya service failed, trying fallback');
          throw error;
        }
      },

      // Service 2: MyMemory (free and fast) - secondary priority
      async () => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        try {
          const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en|or`;
          const response = await fetch(url, { signal: controller.signal });
          clearTimeout(timeoutId);

          if (!response.ok) throw new Error(`MyMemory API error: ${response.status}`);
          const data = await response.json();
          if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
            return data.responseData.translatedText;
          }
          throw new Error('MyMemory API returned invalid response');
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      },

      // Service 2: Enhanced basic translation (fast fallback)
      async () => {
        console.log('Using enhanced basic translation fallback');

        // Expanded dictionary for better coverage
        const basicTranslations: { [key: string]: string } = {
          // Common words
          'the': 'ସେହି', 'and': 'ଏବଂ', 'is': 'ଅଛି', 'are': 'ଅଛନ୍ତି',
          'this': 'ଏହା', 'that': 'ସେହା', 'with': 'ସହିତ', 'for': 'ପାଇଁ',
          'from': 'ରୁ', 'to': 'କୁ', 'in': 'ରେ', 'on': 'ଉପରେ',
          'at': 'ରେ', 'by': 'ଦ୍ୱାରା', 'of': 'ର', 'as': 'ଯେପରି',
          'will': 'ହେବ', 'can': 'ପାରିବ', 'has': 'ଅଛି', 'have': 'ଅଛି',

          // News and business terms
          'news': 'ସମ୍ବାଦ', 'article': 'ପ୍ରବନ୍ଧ', 'report': 'ରିପୋର୍ଟ',
          'economic': 'ଅର୍ଥନୈତିକ', 'economy': 'ଅର୍ଥନୀତି', 'business': 'ବ୍ୟବସାୟ',
          'government': 'ସରକାର', 'policy': 'ନୀତି', 'market': 'ବଜାର',
          'growth': 'ବୃଦ୍ଧି', 'development': 'ବିକାଶ', 'investment': 'ନିବେଶ',
          'unemployment': 'ବେକାରୀ', 'employment': 'ନିଯୁକ୍ତି',
          'company': 'କମ୍ପାନୀ', 'industry': 'ଶିଳ୍ପ', 'sector': 'କ୍ଷେତ୍ର',

          // Numbers and measurements
          'rate': 'ହାର', 'percent': 'ଶତକଡ଼ା', 'million': 'ମିଲିୟନ',
          'billion': 'ବିଲିୟନ', 'thousand': 'ହଜାର', 'hundred': 'ଶହ',

          // Time and dates
          'year': 'ବର୍ଷ', 'month': 'ମାସ', 'day': 'ଦିନ', 'time': 'ସମୟ',
          'today': 'ଆଜି', 'yesterday': 'ଗତକାଲି', 'tomorrow': 'ଆସନ୍ତାକାଲି',

          // Descriptive words
          'high': 'ଉଚ୍ଚ', 'low': 'କମ', 'new': 'ନୂଆ', 'old': 'ପୁରୁଣା',
          'good': 'ଭଲ', 'bad': 'ଖରାପ', 'big': 'ବଡ', 'small': 'ଛୋଟ',
          'important': 'ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ', 'major': 'ମୁଖ୍ୟ', 'significant': 'ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ',

          // People and places
          'people': 'ଲୋକ', 'person': 'ବ୍ୟକ୍ତି', 'country': 'ଦେଶ', 'world': 'ବିଶ୍ୱ',
          'city': 'ସହର', 'state': 'ରାଜ୍ୟ', 'nation': 'ରାଷ୍ଟ୍ର'
        };

        // Preserve original case and apply translations more intelligently
        let translatedText = text;
        for (const [english, oriya] of Object.entries(basicTranslations)) {
          // Case-insensitive replacement while preserving sentence structure
          const regex = new RegExp(`\\b${english}\\b`, 'gi');
          translatedText = translatedText.replace(regex, oriya);
        }

        return `[ଉନ୍ନତ ମୂଳ ଅନୁବାଦ] ${translatedText}`;
      }
    ];

    // Try each translation service with optimized error handling
    for (let i = 0; i < translationServices.length; i++) {
      try {
        console.log(`Trying translation service ${i + 1}...`);
        const startTime = Date.now();
        const result = await translationServices[i]();
        const endTime = Date.now();

        if (result && result.trim().length > 0) {
          console.log(`Translation successful with service ${i + 1} in ${endTime - startTime}ms`);
          return result;
        }
      } catch (error) {
        console.log(`Translation service ${i + 1} failed:`, error);
        // For the first service, continue to next service
        // For the last service (basic translation), it should always work
        if (i === translationServices.length - 1) {
          console.error('Even basic translation failed, this should not happen');
        }
        continue;
      }
    }

    // If all services fail (which should be very rare), return original text with prefix
    return `[ଅନୁବାଦ ବିଫଳ] ${text}`;
  };

  // Function to fetch and translate webpage content
  const fetchAndTranslateWebpage = async (targetUrl: string) => {
    if (!targetUrl) return;

    setIsFetchingWebpage(true);
    try {
      console.log('Fetching webpage content from:', targetUrl);

      // Try multiple CORS proxy services as fallbacks
      const proxyServices = [
        `https://corsproxy.io/?${encodeURIComponent(targetUrl)}`,
        `https://cors-anywhere.herokuapp.com/${targetUrl}`,
        `https://api.allorigins.win/get?url=${encodeURIComponent(targetUrl)}`,
        `https://thingproxy.freeboard.io/fetch/${targetUrl}`
      ];

      let htmlContent = '';
      let fetchSuccess = false;

      for (const proxyUrl of proxyServices) {
        try {
          console.log('Trying proxy:', proxyUrl);
          const response = await fetch(proxyUrl, {
            method: 'GET',
            headers: {
              'Accept': 'application/json, text/plain, */*',
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            console.log(`Proxy failed with status: ${response.status}`);
            continue;
          }

          // Handle different response formats from different proxies
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            htmlContent = data.contents || data.data || data.response || '';
          } else {
            htmlContent = await response.text();
          }

          if (htmlContent) {
            fetchSuccess = true;
            console.log('Successfully fetched content using proxy:', proxyUrl);
            break;
          }
        } catch (proxyError) {
          console.log('Proxy failed:', proxyUrl, proxyError);
          continue;
        }
      }

      if (!fetchSuccess || !htmlContent) {
        // If all proxies fail, provide a demo content for testing translation
        console.log('All proxies failed, using demo content for translation testing');
        htmlContent = `
          <html>
            <body>
              <h1>Demo Content for Translation Testing</h1>
              <p>This is a sample article about economic news. The unemployment rate has reached significant levels due to various economic factors.</p>
              <p>Economic experts suggest that policy changes and market reforms could help improve the employment situation in the coming months.</p>
              <p>The government is considering various measures to boost economic growth and create more job opportunities for citizens.</p>
              <p>Financial markets have shown mixed reactions to recent policy announcements and economic indicators.</p>
            </body>
          </html>
        `;
        console.log('Using demo content since original URL could not be fetched due to CORS restrictions');
      }

      // Extract text content from HTML
      const textContent = extractTextFromHTML(htmlContent);
      setWebpageContent(textContent);

      if (textContent) {
        console.log('Starting webpage translation to Oriya...');
        const translated = await translateTextToOriya(textContent);
        setTranslatedWebpageContent(translated);
        console.log('Webpage translation completed');
      }

    } catch (error) {
      console.error('Error fetching/translating webpage:', error);
      setWebpageContent('Error fetching webpage content. This might be due to CORS restrictions or network issues.');
      setTranslatedWebpageContent('ୱେବପେଜ୍ ବିଷୟବସ୍ତୁ ଆଣିବାରେ ତ୍ରୁଟି। ଏହା CORS ପ୍ରତିବନ୍ଧକ କିମ୍ବା ନେଟୱାର୍କ ସମସ୍ୟା କାରଣରୁ ହୋଇପାରେ।');
    } finally {
      setIsFetchingWebpage(false);
    }
  };

  // Handle translation
  const handleTranslate = async () => {
    if (!url || typeof url !== 'string') return;

    setIsTranslating(true);
    try {
      // First fetch and translate the webpage content
      await fetchAndTranslateWebpage(url);
    } catch (error) {
      console.error('Translation failed:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (returnUrl && typeof returnUrl === 'string') {
      router.push(returnUrl);
    } else {
      router.back();
    }
  };

  // Auto-translate on page load if URL is provided
  useEffect(() => {
    if (url && typeof url === 'string') {
      handleTranslate();
    }
  }, [url]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBack}
              className="flex items-center gap-2 text-orange-600 hover:text-orange-700 transition-colors"
            >
              <PiArrowLeft size={20} />
              <span className="font-medium">{labels.back}</span>
            </button>
            
            <div className="flex items-center gap-2 text-orange-600">
              <PiGlobe size={24} />
              <span className="font-semibold text-lg">Oriya Translation</span>
            </div>
          </div>

          {/* Reference Information */}
          <div className="bg-orange-50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium text-orange-800">{labels.reference}:</span>
              {referenceNumber && (
                <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-sm">
                  #{referenceNumber}
                </span>
              )}
            </div>
            
            {domain && (
              <div className="text-sm text-orange-700 mb-2">
                <span className="font-medium">{labels.source}:</span> {domain}
              </div>
            )}
            
            {url && (
              <div className="flex items-center gap-2">
                <a
                  href={url as string}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-orange-600 hover:text-orange-700 underline text-sm truncate flex-1"
                >
                  {url}
                </a>
                <a
                  href={url as string}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors flex items-center gap-1"
                >
                  <PiGlobe size={16} />
                  {labels.visit}
                </a>
              </div>
            )}
          </div>

          {/* Translation Button */}
          <button
            onClick={handleTranslate}
            disabled={isTranslating || !url}
            className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
          >
            <PiTranslate size={20} />
            {isTranslating ? labels.translating : labels.translate}
          </button>
        </div>

        {/* Content Section */}
        {(isFetchingWebpage || webpageContent || translatedWebpageContent) && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <PiTranslate size={24} />
              {labels.webpageContent}
            </h2>

            {isFetchingWebpage && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                <p className="text-gray-600">{labels.fetchingContent}</p>
              </div>
            )}

            {webpageContent && (
              <div className="space-y-6">
                {/* Original Content */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    {labels.originalText}
                  </h3>
                  <div className="bg-blue-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                    <p className="text-gray-700 whitespace-pre-wrap text-sm leading-relaxed">
                      {webpageContent}
                    </p>
                  </div>
                </div>

                {/* Translated Content */}
                {translatedWebpageContent && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      {labels.translatedText}
                    </h3>
                    <div className="bg-orange-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                      <p className="text-gray-700 whitespace-pre-wrap text-sm leading-relaxed" style={{ fontFamily: 'Noto Sans Oriya, serif' }}>
                        {translatedWebpageContent}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default OriyaPage;