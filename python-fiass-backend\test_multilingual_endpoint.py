#!/usr/bin/env python3
"""
Test the multilingual financial query endpoint with Telugu, Kannada, and Oriya
"""

import requests
import json
import sys
import os

def test_multilingual_endpoint():
    """Test the multilingual endpoint with different languages"""
    
    # Test cases for different languages
    test_cases = [
        {
            'language': 'Telugu',
            'query': 'వ్యవసాయ అభివృద్ధికి ఎలాంటి ఆర్థిక సహాయం అందుబాటులో ఉంది?',
            'expected_lang': 'te'
        },
        {
            'language': 'Kannada', 
            'query': 'ಕೃಷಿ ಅಭಿವೃದ್ಧಿಗೆ ಯಾವ ರೀತಿಯ ಆರ್ಥಿಕ ಸಹಾಯ ಲಭ್ಯವಿದೆ?',
            'expected_lang': 'kn'
        },
        {
            'language': 'Oriya',
            'query': 'କୃଷି ବିକାଶ ପାଇଁ କେଉଁ ପ୍ରକାର ଆର୍ଥିକ ସହାୟତା ଉପଲବ୍ଧ?',
            'expected_lang': 'or'
        },
        {
            'language': 'Tamil',
            'query': 'விவசாய வளர்ச்சிக்கு என்ன வகையான நிதி உதவி கிடைக்கிறது?',
            'expected_lang': 'ta'
        }
    ]
    
    base_url = "http://localhost:8000"
    endpoint = "/api/multilingual_financial_query"
    
    print("🧪 Testing Multilingual Financial Query Endpoint")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['language']}")
        print(f"Query: {test_case['query']}")
        print("-" * 40)
        
        # Prepare request data
        request_data = {
            "query": test_case['query'],
            "language": test_case['language'],
            "enable_translation": True
        }
        
        try:
            # Make the request
            response = requests.post(
                f"{base_url}{endpoint}",
                json=request_data,
                headers={'Content-Type': 'application/json'},
                timeout=60
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # Check if translation was successful
                if 'ai_response' in result:
                    ai_response = result['ai_response']
                    print(f"✅ Response received: {ai_response[:100]}...")
                    
                    # Check if response is in the expected language
                    if test_case['expected_lang'] == 'te' and any(ord(c) >= 0x0C00 and ord(c) <= 0x0C7F for c in ai_response):
                        print("✅ Response appears to be in Telugu")
                    elif test_case['expected_lang'] == 'kn' and any(ord(c) >= 0x0C80 and ord(c) <= 0x0CFF for c in ai_response):
                        print("✅ Response appears to be in Kannada")
                    elif test_case['expected_lang'] == 'or' and any(ord(c) >= 0x0B00 and ord(c) <= 0x0B7F for c in ai_response):
                        print("✅ Response appears to be in Oriya")
                    elif test_case['expected_lang'] == 'ta' and any(ord(c) >= 0x0B80 and ord(c) <= 0x0BFF for c in ai_response):
                        print("✅ Response appears to be in Tamil")
                    else:
                        print("⚠️ Response might be in English or translation failed")
                        print(f"   First 200 chars: {ai_response[:200]}")
                
                # Check for translation metadata
                if 'translation_metadata' in result:
                    metadata = result['translation_metadata']
                    print(f"📊 Translation metadata: {metadata}")
                
                # Check for related questions
                if 'related_questions' in result and result['related_questions']:
                    print(f"❓ Related questions: {len(result['related_questions'])} found")
                    for j, question in enumerate(result['related_questions'][:2], 1):
                        print(f"   {j}. {question[:80]}...")
                
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Response: {response.text[:500]}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out")
        except requests.exceptions.ConnectionError:
            print("❌ Connection error - is the server running?")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n📊 Test Summary")
    print("=" * 60)
    print("If you see responses in the respective regional languages,")
    print("the translation system is working correctly!")
    print("\nIf responses are in English, check:")
    print("1. Google Translate rate limiting")
    print("2. Backup provider availability (MyMemory, LibreTranslate)")
    print("3. Translation service configuration")

if __name__ == '__main__':
    test_multilingual_endpoint()
