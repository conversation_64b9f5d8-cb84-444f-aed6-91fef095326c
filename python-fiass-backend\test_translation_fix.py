"""
Quick test to verify the translation fix is working
"""

import requests
import json
import time

def test_oriya_translation():
    """Test that Oriya queries get Oriya responses"""
    
    print("🧪 Testing Oriya Translation Fix")
    print("=" * 50)
    
    # Test with a simple Oriya query
    oriya_query = "ନିବେଶ ବିଷୟରେ କିଛି ପରାମର୍ଶ ଦିଅନ୍ତୁ"  # Give investment advice
    
    print(f"📝 Query: {oriya_query}")
    print("🎯 Expected: Response should be in Oriya, not English")
    print()
    
    url = "http://localhost:5010/financial_query"
    data = {"query": oriya_query}
    
    try:
        print("📤 Sending request...")
        start_time = time.time()
        
        response = requests.post(url, json=data, timeout=60)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Request successful!")
            print()
            
            # Check language detection
            detected_lang = result.get('detected_language', 'unknown')
            query_lang = result.get('query_language', 'unknown')
            processing_lang = result.get('processing_language', 'unknown')
            
            print(f"🔍 Detected language: {detected_lang}")
            print(f"🔤 Query language: {query_lang}")
            print(f"🌐 Processing language: {processing_lang}")
            print()
            
            # Check response content
            ai_response = result.get('ai_response', result.get('response', ''))
            
            if ai_response:
                print("📄 Response Analysis:")
                print("-" * 40)
                
                # Check for Oriya characters
                import re
                oriya_pattern = re.compile(r'[\u0B00-\u0B7F]')
                oriya_chars = len(oriya_pattern.findall(ai_response))
                total_chars = len([c for c in ai_response if c.isalpha()])
                
                print(f"📊 Response length: {len(ai_response)} characters")
                print(f"📊 Oriya characters: {oriya_chars}")
                print(f"📊 Total alphabetic characters: {total_chars}")
                
                if total_chars > 0:
                    oriya_ratio = oriya_chars / total_chars
                    print(f"📊 Oriya character ratio: {oriya_ratio:.2%}")
                    
                    if oriya_ratio > 0.7:
                        print("✅ SUCCESS: Response is primarily in Oriya!")
                    elif oriya_ratio > 0.3:
                        print("⚠️  PARTIAL: Response contains significant Oriya")
                    elif oriya_ratio > 0.1:
                        print("⚠️  MINIMAL: Response contains some Oriya")
                    else:
                        print("❌ FAILED: Response is not in Oriya")
                
                print()
                print("📝 Response Preview:")
                print("-" * 40)
                print(ai_response[:300] + "..." if len(ai_response) > 300 else ai_response)
                print("-" * 40)
                
                # Check method used
                method = result.get('method', 'unknown')
                print(f"🔧 Method used: {method}")
                
                # Check for translation indicators
                if '[Translation Failed]' in ai_response:
                    print("❌ Translation failed - English response returned")
                elif '[Enhancement Error]' in ai_response:
                    print("❌ Enhancement error - English response returned")
                else:
                    print("✅ No error indicators found")
                
            else:
                print("❌ No response content found")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_english_query():
    """Test that English queries still work"""
    
    print("\n" + "=" * 50)
    print("🧪 Testing English Query (Control Test)")
    print("=" * 50)
    
    english_query = "Give me investment advice"
    
    print(f"📝 Query: {english_query}")
    print("🎯 Expected: Response should be in English")
    print()
    
    url = "http://localhost:5010/financial_query"
    data = {"query": english_query}
    
    try:
        print("📤 Sending request...")
        response = requests.post(url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('ai_response', result.get('response', ''))
            
            print("✅ English query successful!")
            print(f"📝 Response preview: {ai_response[:200]}...")
            
        else:
            print(f"❌ English query failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ English query error: {e}")

if __name__ == "__main__":
    # Test Oriya translation
    test_oriya_translation()
    
    # Test English control
    test_english_query()
    
    print("\n" + "=" * 50)
    print("🏁 TRANSLATION TEST COMPLETE")
    print("=" * 50)
    print()
    print("✅ If the Oriya response shows high Oriya character ratio,")
    print("   the translation fix is working correctly!")
    print()
    print("❌ If the response is still in English, the translation")
    print("   needs further debugging.")
