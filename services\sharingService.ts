import { Chat } from "@/stores/chatList";

export interface ShareableChat {
  id: string;
  shareId: string;
  title: string;
  messages: any[];
  createdAt: string;
  sharedAt: string;
  expiresAt?: string;
  isPublic: boolean;
  includeMessages: boolean;
  viewCount: number;
  originalChatId: string;
}

export interface ShareSettings {
  includeMessages: boolean;
  publicAccess: boolean;
  expiresIn: string;
}

class SharingService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  }

  /**
   * Generate a unique share ID
   */
  private generateShareId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Calculate expiration date based on settings
   */
  private calculateExpirationDate(expiresIn: string): Date | null {
    if (expiresIn === 'never') return null;
    
    const now = new Date();
    switch (expiresIn) {
      case '1day':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case '7days':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case '30days':
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      default:
        return null;
    }
  }

  /**
   * Create a shareable version of a chat
   */
  async createShareableChat(
    chat: Chat, 
    settings: ShareSettings
  ): Promise<{ shareId: string; shareUrl: string }> {
    try {
      const shareId = this.generateShareId();
      const expiresAt = this.calculateExpirationDate(settings.expiresIn);
      
      // Filter messages based on settings
      const messagesToShare = settings.includeMessages 
        ? chat.messages 
        : chat.messages.slice(0, 1); // Only include first message if not sharing all

      const shareableChat: ShareableChat = {
        id: shareId,
        shareId,
        title: chat.title,
        messages: messagesToShare,
        createdAt: chat.createdAt,
        sharedAt: new Date().toISOString(),
        expiresAt: expiresAt?.toISOString(),
        isPublic: settings.publicAccess,
        includeMessages: settings.includeMessages,
        viewCount: 0,
        originalChatId: chat.id
      };

      // In a real implementation, you would save this to your backend
      // For now, we'll store it in localStorage as a demo
      const existingShares = this.getStoredShares();
      existingShares[shareId] = shareableChat;
      localStorage.setItem('sharedChats', JSON.stringify(existingShares));

      const shareUrl = `${this.baseUrl}/shared/chat/${shareId}`;
      
      return { shareId, shareUrl };
    } catch (error) {
      console.error('Failed to create shareable chat:', error);
      throw new Error('Failed to create share link');
    }
  }

  /**
   * Get a shared chat by share ID
   */
  async getSharedChat(shareId: string): Promise<ShareableChat | null> {
    try {
      // In a real implementation, this would fetch from your backend
      const storedShares = this.getStoredShares();
      const sharedChat = storedShares[shareId];
      
      if (!sharedChat) {
        return null;
      }

      // Check if the share has expired
      if (sharedChat.expiresAt && new Date() > new Date(sharedChat.expiresAt)) {
        // Remove expired share
        delete storedShares[shareId];
        localStorage.setItem('sharedChats', JSON.stringify(storedShares));
        return null;
      }

      // Increment view count
      sharedChat.viewCount += 1;
      storedShares[shareId] = sharedChat;
      localStorage.setItem('sharedChats', JSON.stringify(storedShares));

      return sharedChat;
    } catch (error) {
      console.error('Failed to get shared chat:', error);
      return null;
    }
  }

  /**
   * Get all shares for the current user
   */
  getUserShares(): ShareableChat[] {
    try {
      const storedShares = this.getStoredShares();
      return Object.values(storedShares).filter(share => !this.isExpired(share));
    } catch (error) {
      console.error('Failed to get user shares:', error);
      return [];
    }
  }

  /**
   * Delete a shared chat
   */
  async deleteShare(shareId: string): Promise<boolean> {
    try {
      const storedShares = this.getStoredShares();
      if (storedShares[shareId]) {
        delete storedShares[shareId];
        localStorage.setItem('sharedChats', JSON.stringify(storedShares));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to delete share:', error);
      return false;
    }
  }

  /**
   * Check if a share has expired
   */
  private isExpired(share: ShareableChat): boolean {
    return share.expiresAt ? new Date() > new Date(share.expiresAt) : false;
  }

  /**
   * Get stored shares from localStorage
   */
  private getStoredShares(): Record<string, ShareableChat> {
    try {
      const stored = localStorage.getItem('sharedChats');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Failed to parse stored shares:', error);
      return {};
    }
  }

  /**
   * Clean up expired shares
   */
  cleanupExpiredShares(): void {
    try {
      const storedShares = this.getStoredShares();
      const validShares: Record<string, ShareableChat> = {};
      
      Object.entries(storedShares).forEach(([shareId, share]) => {
        if (!this.isExpired(share)) {
          validShares[shareId] = share;
        }
      });
      
      localStorage.setItem('sharedChats', JSON.stringify(validShares));
    } catch (error) {
      console.error('Failed to cleanup expired shares:', error);
    }
  }

  /**
   * Import a shared chat into user's chat list safely
   */
  async importSharedChat(shareId: string): Promise<{ success: boolean; chatId?: string; error?: string }> {
    try {
      // Get the shared chat
      const sharedChat = await this.getSharedChat(shareId);
      if (!sharedChat) {
        return { success: false, error: 'Shared chat not found or expired' };
      }

      // Import the chat handler
      const { useChatHandler } = await import("@/stores/chatList");
      const { addChatSafely } = useChatHandler.getState();

      // Create a new chat based on the shared chat
      const newChatId = `imported-${Date.now()}`;
      const newChat = {
        id: newChatId,
        title: `${sharedChat.title} (Continued)`,
        createdAt: new Date().toISOString(),
        messages: sharedChat.messages.map(msg => ({
          ...msg,
          timestamp: msg.timestamp || new Date().toISOString()
        })),
        indexUsed: 'default'
      };

      // Safely add to user's chat list (preserves existing chats)
      await addChatSafely(newChat);

      return { success: true, chatId: newChatId };
    } catch (error) {
      console.error('Failed to import shared chat:', error);
      return { success: false, error: 'Failed to import chat. Please try again.' };
    }
  }

  /**
   * Generate share metadata for social media
   */
  generateShareMetadata(chat: Chat): {
    title: string;
    description: string;
    image?: string;
  } {
    const messageCount = chat.messages.length;
    const userMessages = chat.messages.filter(msg => msg.isUser).length;
    
    return {
      title: `AIQuill Conversation: ${chat.title}`,
      description: `Check out this conversation with ${messageCount} messages and ${userMessages} questions on AIQuill.`,
      // You could add an image here if you generate conversation previews
    };
  }
}

export const sharingService = new SharingService();
export default sharingService;
