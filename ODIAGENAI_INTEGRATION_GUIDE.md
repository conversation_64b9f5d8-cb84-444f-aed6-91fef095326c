# OdiaGenAI Integration Guide

## Overview

This integration enhances your Oriya/Odia language translation capabilities by leveraging specialized models from OdiaGenAI.org. OdiaGenAI provides state-of-the-art language models specifically trained for Odia language tasks, offering superior translation quality compared to general-purpose translation services.

## Features

### 🌟 Enhanced Translation Quality
- **Specialized Models**: Uses OdiaGenAI's Llama2-based models trained specifically for Odia
- **Cultural Context**: Better understanding of Odia cultural nuances and expressions
- **Domain Expertise**: Improved handling of technical, business, and everyday language

### 🚀 Multiple Model Support
- **odia_llama2_base**: Primary model for general translation tasks
- **odia_llama7b_v1**: Advanced model for complex translations
- **Auto Selection**: Intelligent model selection based on content type

### 🔄 Seamless Integration
- **Priority System**: OdiaGenAI is prioritized for Oriya translations
- **Fallback Support**: Graceful fallback to existing translation services
- **Frontend Integration**: Enhanced Oriya page with OdiaGenAI support

## Setup Instructions

### 1. Environment Configuration

#### Required API Token
You need a Hugging Face API token to access OdiaGenAI models:

1. Visit [Hugging Face](https://huggingface.co/settings/tokens)
2. Create a new token with "Read" permissions
3. Set the environment variable:

```bash
# Linux/Mac
export HUGGINGFACE_API_TOKEN=your_token_here

# Windows
set HUGGINGFACE_API_TOKEN=your_token_here

# Or add to your .env file
echo "HUGGINGFACE_API_TOKEN=your_token_here" >> .env
```

### 2. Backend Setup

The integration is already added to your Flask backend. Ensure the following files are present:

- `python-fiass-backend/services/odiagenai_service.py` ✅
- Updated `python-fiass-backend/services/enhanced_indian_translation.py` ✅
- Updated `python-fiass-backend/services/translation_service.py` ✅
- Updated `python-fiass-backend/full_code.py` with new endpoint ✅

### 3. Frontend Setup

The frontend integration is already updated:

- Updated `pages/oriya.tsx` with OdiaGenAI priority ✅
- Updated `pages/api/translate.js` with new action ✅

### 4. Testing the Integration

Run the comprehensive test suite:

```bash
cd python-fiass-backend
python test_odiagenai_integration.py
```

## Usage

### 1. Direct Service Usage

```python
from services.odiagenai_service import odiagenai_translator

# Translate English to Odia
result = odiagenai_translator.translate_with_odiagenai(
    text="Hello, how are you?",
    source_lang="en",
    target_lang="or",
    model_preference="auto"
)

if result['success']:
    print(f"Translation: {result['translated_text']}")
    print(f"Model used: {result['model']}")
    print(f"Confidence: {result['confidence']}")
```

### 2. Backend API Usage

```bash
curl -X POST http://localhost:8000/translate-odiagenai \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Good morning! How can I help you?",
    "source_lang": "en",
    "target_lang": "or",
    "model_preference": "auto"
  }'
```

### 3. Frontend Usage

The Oriya page (`http://localhost:3000/oriya`) now automatically uses OdiaGenAI for translations. Users will see improved translation quality with the `[OdiaGenAI]` prefix indicating the enhanced service is being used.

## Translation Directions Supported

| Source | Target | Status | Description |
|--------|--------|--------|-------------|
| English | Odia | ✅ | Primary use case |
| Odia | English | ✅ | Reverse translation |
| Hindi | Odia | ✅ | Regional language support |
| Odia | Hindi | ✅ | Regional language support |

## Model Information

### Available Models

1. **odia_llama2_base** (Default)
   - Model: `OdiaGenAI/odia_llama2_7B_base`
   - Best for: General translation tasks
   - Training: 180k Odia instruction set

2. **odia_llama7b_v1**
   - Model: `OdiaGenAI/odiagenAI_llama7b_base_v1`
   - Best for: Complex translations
   - Training: Enhanced instruction following

### Model Selection

- **Auto Mode**: Automatically selects the best model for the task
- **Manual Selection**: Specify model preference in API calls
- **Fallback**: Graceful fallback to standard translation if OdiaGenAI fails

## Performance Characteristics

### Expected Response Times
- Simple translations: 5-15 seconds
- Complex translations: 15-30 seconds
- Model loading (first request): 30-60 seconds

### Quality Improvements
- **Cultural Accuracy**: Better handling of Odia cultural expressions
- **Technical Terms**: Improved translation of business and technical vocabulary
- **Grammar**: More natural Odia sentence structure
- **Context**: Better preservation of meaning and context

## Troubleshooting

### Common Issues

#### 1. "OdiaGenAI service not available"
- **Cause**: Missing Hugging Face API token
- **Solution**: Set `HUGGINGFACE_API_TOKEN` environment variable

#### 2. "Model is loading" (503 errors)
- **Cause**: Hugging Face model needs to warm up
- **Solution**: Wait 30-60 seconds and retry

#### 3. Slow response times
- **Cause**: Cold start or high API load
- **Solution**: First request may be slow; subsequent requests are faster

#### 4. Translation fallback to basic service
- **Cause**: OdiaGenAI service temporarily unavailable
- **Solution**: Check API token and network connectivity

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

### Health Check

Test the service availability:

```python
from services.odiagenai_service import odiagenai_translator

if odiagenai_translator.is_available():
    print("✅ OdiaGenAI service is available")
else:
    print("❌ OdiaGenAI service is not available")
    print("Check HUGGINGFACE_API_TOKEN environment variable")
```

## API Reference

### OdiaGenAI Service Methods

#### `translate_with_odiagenai(text, source_lang, target_lang, model_preference)`

**Parameters:**
- `text` (str): Text to translate
- `source_lang` (str): Source language code ('en', 'or', 'hi')
- `target_lang` (str): Target language code ('en', 'or', 'hi')
- `model_preference` (str): Model preference ('auto', 'odia_llama2_base', 'odia_llama7b_v1')

**Returns:**
```python
{
    'success': bool,
    'translated_text': str,
    'provider': 'odiagenai',
    'model': str,
    'confidence': float,
    'processing_time': float,
    'error': str  # Only if success=False
}
```

### Flask Endpoint

#### `POST /translate-odiagenai`

**Request Body:**
```json
{
    "text": "Text to translate",
    "source_lang": "en",
    "target_lang": "or",
    "model_preference": "auto"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "original_text": "Text to translate",
        "translated_text": "Translated text",
        "source_language": "en",
        "target_language": "or",
        "translation_provider": "odiagenai",
        "model": "odia_llama2_base",
        "confidence": 0.92,
        "processing_time": 12.34
    }
}
```

## Contributing

To improve the OdiaGenAI integration:

1. **Model Updates**: Monitor OdiaGenAI releases for new models
2. **Prompt Engineering**: Optimize translation prompts for better results
3. **Caching**: Implement caching for frequently translated content
4. **Batch Processing**: Add support for batch translations

## Resources

- **OdiaGenAI Website**: https://www.odiagenai.org/
- **Hugging Face Models**: https://huggingface.co/OdiaGenAI
- **Research Papers**: Available on OdiaGenAI website
- **Community**: Join OdiaGenAI community for updates and support

## License

This integration respects OdiaGenAI's licensing terms. The models are available for research and non-commercial purposes. For commercial use, please contact OdiaGenAI team.

---

**Note**: This integration significantly improves Oriya translation quality by leveraging specialized AI models trained specifically for the Odia language. The enhanced accuracy and cultural appropriateness make it ideal for applications serving Odia-speaking users.
