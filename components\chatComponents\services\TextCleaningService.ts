/**
 * TextCleaningService - Service for cleaning repetitive words from text
 * Particularly useful for speech-to-text input and translation artifacts
 */

export interface CleaningAnalysis {
  hasRepetition: boolean;
  repetitionRatio: number;
  repeatedWords: string[];
  originalLength: number;
  cleanedLength: number;
}

export interface CleaningResult {
  cleaned: string;
  wasCorrupted: boolean;
  analysis: CleaningAnalysis;
}

export class TextCleaningService {
  /**
   * Clean repetitive words from text with detailed analysis
   */
  static cleanRepetitiveText(text: string): CleaningAnalysis & { cleanedText: string } {
    if (!text || text.trim().length === 0) {
      return {
        hasRepetition: false,
        repetitionRatio: 0,
        repeatedWords: [],
        cleanedText: text,
        originalLength: 0,
        cleanedLength: 0
      };
    }

    const originalLength = text.length;
    
    // Split text into sentences for better processing
    const sentences = text.split(/[.!?।]+/).filter(s => s.trim().length > 0);
    const cleanedSentences: string[] = [];

    let totalRepeatedWords: string[] = [];

    for (const sentence of sentences) {
      const cleanedSentence = this.cleanSentenceRepetition(sentence.trim());
      cleanedSentences.push(cleanedSentence.cleaned);
      totalRepeatedWords.push(...cleanedSentence.repeatedWords);
    }

    const cleanedText = cleanedSentences.join('. ').trim();
    const cleanedLength = cleanedText.length;
    
    // Calculate repetition ratio
    const repetitionRatio = totalRepeatedWords.length > 0 ? 
      (originalLength - cleanedLength) / originalLength : 0;

    return {
      hasRepetition: totalRepeatedWords.length > 0,
      repetitionRatio,
      repeatedWords: [...new Set(totalRepeatedWords)], // Remove duplicates
      cleanedText: cleanedText + (cleanedText.endsWith('.') ? '' : '.'),
      originalLength,
      cleanedLength
    };
  }

  /**
   * Clean repetitive words from a single sentence
   */
  private static cleanSentenceRepetition(sentence: string): { cleaned: string; repeatedWords: string[] } {
    if (!sentence || sentence.trim().length === 0) {
      return { cleaned: sentence, repeatedWords: [] };
    }

    // Split into words, preserving punctuation
    const words = sentence.split(/\s+/).filter(word => word.trim().length > 0);
    const cleanedWords: string[] = [];
    const repeatedWords: string[] = [];
    
    // Track word frequency in a sliding window
    const windowSize = 5; // Look for repetition in the last 5 words
    
    for (let i = 0; i < words.length; i++) {
      const currentWord = words[i].toLowerCase().replace(/[^\w\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/g, ''); // Keep Tamil, Telugu, Kannada, Oriya characters
      
      if (currentWord.length === 0) {
        cleanedWords.push(words[i]);
        continue;
      }

      // Check if this word appears in the recent window
      let isRepetitive = false;
      const startIndex = Math.max(0, cleanedWords.length - windowSize);
      
      for (let j = startIndex; j < cleanedWords.length; j++) {
        const previousWord = cleanedWords[j].toLowerCase().replace(/[^\w\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0B00-\u0B7F]/g, '');
        
        if (previousWord === currentWord && currentWord.length > 2) { // Only consider words longer than 2 characters
          isRepetitive = true;
          repeatedWords.push(currentWord);
          break;
        }
      }

      // Add word only if it's not repetitive
      if (!isRepetitive) {
        cleanedWords.push(words[i]);
      }
    }

    return {
      cleaned: cleanedWords.join(' '),
      repeatedWords
    };
  }

  /**
   * Comprehensive cleaning method for translation responses
   * Detects if text was corrupted and provides detailed analysis
   */
  static cleanTranslationResponse(text: string): CleaningResult {
    if (!text || text.trim().length === 0) {
      return {
        cleaned: text,
        wasCorrupted: false,
        analysis: {
          hasRepetition: false,
          repetitionRatio: 0,
          repeatedWords: [],
          originalLength: 0,
          cleanedLength: 0
        }
      };
    }

    const result = this.cleanRepetitiveText(text);
    
    // Consider text corrupted if repetition ratio is above 10%
    const wasCorrupted = result.repetitionRatio > 0.1;

    return {
      cleaned: result.cleanedText,
      wasCorrupted,
      analysis: {
        hasRepetition: result.hasRepetition,
        repetitionRatio: result.repetitionRatio,
        repeatedWords: result.repeatedWords,
        originalLength: result.originalLength,
        cleanedLength: result.cleanedLength
      }
    };
  }

  /**
   * Quick cleaning method - returns only the cleaned text
   */
  static quickClean(text: string): string {
    if (!text || text.trim().length === 0) {
      return text;
    }

    const result = this.cleanRepetitiveText(text);
    return result.cleanedText;
  }

  /**
   * Deep cleaning method for heavily corrupted text
   * Uses multiple passes and more aggressive cleaning
   */
  static deepCleanText(text: string): CleaningResult {
    if (!text || text.trim().length === 0) {
      return {
        cleaned: text,
        wasCorrupted: false,
        analysis: {
          hasRepetition: false,
          repetitionRatio: 0,
          repeatedWords: [],
          originalLength: 0,
          cleanedLength: 0
        }
      };
    }

    let currentText = text;
    let totalRepeatedWords: string[] = [];
    let passes = 0;
    const maxPasses = 3;

    // Multiple cleaning passes for heavily corrupted text
    while (passes < maxPasses) {
      const result = this.cleanRepetitiveText(currentText);
      
      if (!result.hasRepetition) {
        break; // No more repetition found
      }

      currentText = result.cleanedText;
      totalRepeatedWords.push(...result.repeatedWords);
      passes++;
    }

    const finalLength = currentText.length;
    const originalLength = text.length;
    const repetitionRatio = originalLength > 0 ? (originalLength - finalLength) / originalLength : 0;

    return {
      cleaned: currentText,
      wasCorrupted: repetitionRatio > 0.05, // Consider corrupted if 5% or more was removed
      analysis: {
        hasRepetition: totalRepeatedWords.length > 0,
        repetitionRatio,
        repeatedWords: [...new Set(totalRepeatedWords)],
        originalLength,
        cleanedLength: finalLength
      }
    };
  }
}