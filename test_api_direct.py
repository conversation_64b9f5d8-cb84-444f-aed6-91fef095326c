#!/usr/bin/env python3
"""
Test the API directly with proper UTF-8 encoding
"""

import requests
import json

def test_tamil_api():
    """Test Tamil query with proper encoding"""
    try:
        # Tamil query
        tamil_query = "ONGC நிறுவனத்தின் நடுவர் மனு (சிவில்) எண். 05/2022 ஏன்?"
        
        print("🧪 Testing Tamil API with proper UTF-8 encoding...")
        print(f"📝 Query: {tamil_query}")
        print(f"📏 Query length: {len(tamil_query)} characters")
        
        # Test data
        data = {
            "query": tamil_query,
            "language": "Tamil"
        }
        
        # Headers
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        print(f"📤 Sending request to http://localhost:5010/api/multilingual_financial_query")
        print(f"📦 Data: {json.dumps(data, ensure_ascii=False)}")
        
        # Make request
        response = requests.post(
            'http://localhost:5010/api/multilingual_financial_query',
            json=data,
            headers=headers,
            timeout=60
        )
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check key fields
            query_received = result.get('query', '')
            ai_response = result.get('ai_response', '')
            query_language = result.get('query_language', '')
            regional_detected = result.get('regional_language_detected', False)
            processing_flow = result.get('processing_flow', '')
            
            print(f"📝 Query received by server: {query_received}")
            print(f"🔍 Query language detected: {query_language}")
            print(f"🌏 Regional language detected: {regional_detected}")
            print(f"🔄 Processing flow: {processing_flow}")
            print(f"📏 AI response length: {len(ai_response)} characters")
            
            # Check if Tamil characters are preserved
            import re
            tamil_chars_sent = len(re.findall(r'[\u0B80-\u0BFF]', tamil_query))
            tamil_chars_received = len(re.findall(r'[\u0B80-\u0BFF]', query_received))
            tamil_chars_response = len(re.findall(r'[\u0B80-\u0BFF]', ai_response))
            
            print(f"🔤 Tamil chars sent: {tamil_chars_sent}")
            print(f"🔤 Tamil chars received: {tamil_chars_received}")
            print(f"🔤 Tamil chars in response: {tamil_chars_response}")
            
            # Check if routing worked
            if regional_detected and query_language == 'Tamil':
                print("✅ Tamil language detection worked!")
                if 'tamil' in processing_flow.lower():
                    print("✅ Tamil processing flow used!")
                else:
                    print("⚠️ Tamil detected but not using Tamil processing flow")
            else:
                print("❌ Tamil language detection failed")
            
            # Check response completeness
            if len(ai_response) > 200:
                print("✅ Response appears complete (>200 characters)")
            else:
                print("⚠️ Response may be incomplete (<200 characters)")
            
            # Show first part of response
            print(f"\n📄 Response preview (first 200 chars):")
            print(f"   {ai_response[:200]}...")
            
            return True
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_english_comparison():
    """Test English query for comparison"""
    try:
        print("\n🔄 Testing English query for comparison...")
        
        # English query
        english_query = "Why Arbitration Petition (Civil) No. 05 of 2022 by ONGC?"
        
        data = {
            "query": english_query,
            "language": "English"
        }
        
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        response = requests.post(
            'http://localhost:5010/api/multilingual_financial_query',
            json=data,
            headers=headers,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get('ai_response', '')
            
            print(f"📏 English response length: {len(ai_response)} characters")
            print(f"📄 English response preview: {ai_response[:200]}...")
            
            return len(ai_response)
        else:
            print(f"❌ English request failed")
            return 0
            
    except Exception as e:
        print(f"❌ Error testing English API: {e}")
        return 0

if __name__ == "__main__":
    print("🚀 Starting direct API test...\n")
    
    # Test Tamil
    tamil_success = test_tamil_api()
    
    # Test English for comparison
    english_length = test_english_comparison()
    
    print(f"\n📊 Test Results:")
    print(f"   Tamil API Test: {'✅ PASS' if tamil_success else '❌ FAIL'}")
    print(f"   English Response Length: {english_length} characters")
    
    if tamil_success:
        print("\n🎉 Tamil API test completed successfully!")
    else:
        print("\n⚠️ Tamil API test had issues.")
