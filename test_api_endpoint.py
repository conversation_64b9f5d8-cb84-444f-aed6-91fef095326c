#!/usr/bin/env python3
"""
Test the actual API endpoint with a long financial query
"""

import requests
import json
import time

def test_financial_query_api():
    """Test the /api/financial_query endpoint with a long query"""
    print("🧪 Testing Financial Query API Endpoint")
    print("=" * 50)
    
    # Test with a comprehensive Telugu financial query
    test_query = """
    వ్యవసాయ రుణాలు మరియు ఆర్థిక సహాయ పథకాల గురించి వివరంగా చెప్పండి. కిసాన్ క్రెడిట్ కార్డ్, పంట బీమా, వ్యవసాయ టర్మ్ లోన్లు, వడ్డీ రాయితీ పథకాలు, మరియు ప్రాధాన్యత రంగ రుణాల గురించి పూర్తి సమాచారం అవసరం. రైతులకు అందుబాటులో ఉన్న వివిధ ప్రభుత్వ పథకాలు మరియు వాటి అర్హత షరతులు కూడా తెలియజేయండి.
    """
    
    print(f"📏 Query length: {len(test_query)} characters")
    print(f"Query: {test_query[:100]}...")
    
    # API endpoint
    url = "http://localhost:8000/api/financial_query"
    
    payload = {
        "query": test_query,
        "language": "te"
    }
    
    try:
        print(f"\n🚀 Sending request to {url}")
        start_time = time.time()
        
        response = requests.post(url, json=payload, timeout=60)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ Response time: {response_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract the AI response
            ai_response = data.get('ai_response', '')
            
            print(f"\n✅ API Response received")
            print(f"📏 Response length: {len(ai_response)} characters")
            
            if len(ai_response) > 0:
                print(f"📝 Response preview:")
                print(f"   Start: {ai_response[:200]}...")
                print(f"   End: ...{ai_response[-200:]}")
                
                # Check for truncation indicators
                if ai_response.endswith('...') or len(ai_response) < 500:
                    print("⚠️ Possible truncation detected")
                else:
                    print("✅ Response appears complete")
                    
                # Check response quality
                if 'వ్యవసాయ' in ai_response and 'రుణ' in ai_response:
                    print("✅ Response contains relevant Telugu financial terms")
                else:
                    print("⚠️ Response may not be properly translated")
                    
            else:
                print("❌ Empty response received")
                
            # Show additional response fields
            other_fields = ['summary', 'context', 'related_questions']
            for field in other_fields:
                if field in data and data[field]:
                    field_content = data[field]
                    if isinstance(field_content, list):
                        print(f"📋 {field}: {len(field_content)} items")
                    else:
                        print(f"📋 {field}: {len(str(field_content))} characters")
                        
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Is the server running on localhost:8000?")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took longer than 60 seconds")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_multiple_languages():
    """Test with multiple regional languages"""
    print("\n🧪 Testing Multiple Regional Languages")
    print("=" * 50)
    
    test_cases = [
        {
            'language': 'kn',
            'query': 'ಕೃಷಿ ಸಾಲಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಯೋಜನೆಗಳ ಬಗ್ಗೆ ವಿವರವಾಗಿ ತಿಳಿಸಿ',
            'name': 'Kannada'
        },
        {
            'language': 'or',
            'query': 'କୃଷି ଋଣ ଏବଂ ସରକାରୀ ଯୋଜନା ବିଷୟରେ ବିସ୍ତୃତ ସୂଚନା ଦିଅନ୍ତୁ',
            'name': 'Oriya'
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔄 Testing {test_case['name']} ({test_case['language']})")
        print(f"Query: {test_case['query']}")
        
        payload = {
            "query": test_case['query'],
            "language": test_case['language']
        }
        
        try:
            response = requests.post("http://localhost:8000/api/financial_query", 
                                   json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', '')
                print(f"✅ {test_case['name']} response: {len(ai_response)} chars")
                if len(ai_response) > 100:
                    print(f"   Preview: {ai_response[:100]}...")
                else:
                    print(f"   Full: {ai_response}")
            else:
                print(f"❌ {test_case['name']} failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test_case['name']} error: {e}")

def main():
    """Run API endpoint tests"""
    print("🚀 Testing Translation Truncation Fix - API Endpoint")
    print("=" * 60)
    
    # Test 1: Long Telugu financial query
    test_financial_query_api()
    
    # Test 2: Multiple languages
    test_multiple_languages()
    
    print("\n📊 Test Summary")
    print("=" * 60)
    print("This test verifies:")
    print("1. Long queries don't get truncated")
    print("2. Responses are complete and properly translated")
    print("3. All regional languages work correctly")
    print("4. API performance is acceptable")

if __name__ == '__main__':
    main()
