"""
Test script for OdiaGenAI integration
Tests the new OdiaGenAI service for enhanced Oriya translation
"""

import os
import sys
import json
import time
import requests
from typing import Dict, List

# Add the services directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'services'))

def test_odiagenai_service():
    """Test the OdiaGenAI service directly"""
    print("🧪 Testing OdiaGenAI Service Directly")
    print("=" * 50)
    
    try:
        from services.odiagenai_service import odiagenai_translator
        
        # Test cases for different translation directions
        test_cases = [
            {
                "text": "Hello, how are you today?",
                "source_lang": "en",
                "target_lang": "or",
                "description": "English to Oriya - Simple greeting"
            },
            {
                "text": "The weather is very nice today.",
                "source_lang": "en", 
                "target_lang": "or",
                "description": "English to Oriya - Weather description"
            },
            {
                "text": "What is the current economic situation in India?",
                "source_lang": "en",
                "target_lang": "or", 
                "description": "English to Oriya - Economic question"
            },
            {
                "text": "ଆପଣ କେମିତି ଅଛନ୍ତି?",
                "source_lang": "or",
                "target_lang": "en",
                "description": "Oriya to English - Simple question"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 Test Case {i}: {test_case['description']}")
            print(f"📝 Input: {test_case['text']}")
            
            start_time = time.time()
            result = odiagenai_translator.translate_with_odiagenai(
                test_case['text'],
                test_case['source_lang'],
                test_case['target_lang'],
                "auto"
            )
            end_time = time.time()
            
            if result['success']:
                print(f"✅ Success: {result['translated_text']}")
                print(f"⏱️  Time: {end_time - start_time:.2f}s")
                print(f"🤖 Model: {result.get('model', 'unknown')}")
                print(f"🎯 Confidence: {result.get('confidence', 0):.2f}")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
            
            results.append({
                "test_case": test_case,
                "result": result,
                "processing_time": end_time - start_time
            })
            
            # Small delay between requests
            time.sleep(1)
        
        # Print summary
        print("\n📊 Test Summary")
        print("=" * 30)
        successful_tests = sum(1 for r in results if r['result']['success'])
        print(f"✅ Successful: {successful_tests}/{len(results)}")
        print(f"❌ Failed: {len(results) - successful_tests}/{len(results)}")
        
        if successful_tests > 0:
            avg_time = sum(r['processing_time'] for r in results if r['result']['success']) / successful_tests
            print(f"⏱️  Average processing time: {avg_time:.2f}s")
        
        return results
        
    except ImportError as e:
        print(f"❌ Failed to import OdiaGenAI service: {e}")
        return None
    except Exception as e:
        print(f"❌ Error testing OdiaGenAI service: {e}")
        return None

def test_flask_endpoint():
    """Test the Flask endpoint for OdiaGenAI translation"""
    print("\n🌐 Testing Flask Endpoint")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    endpoint = "/translate-odiagenai"
    
    test_cases = [
        {
            "text": "Good morning! How can I help you today?",
            "source_lang": "en",
            "target_lang": "or",
            "model_preference": "auto"
        },
        {
            "text": "The Indian economy is showing positive growth trends.",
            "source_lang": "en",
            "target_lang": "or", 
            "model_preference": "auto"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Endpoint Test {i}")
        print(f"📝 Input: {test_case['text']}")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}{endpoint}",
                json=test_case,
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result_data = data.get('data', {})
                    print(f"✅ Success: {result_data.get('translated_text', 'No translation')}")
                    print(f"⏱️  Time: {end_time - start_time:.2f}s")
                    print(f"🤖 Model: {result_data.get('model', 'unknown')}")
                    print(f"🎯 Confidence: {result_data.get('confidence', 0):.2f}")
                else:
                    print(f"❌ API Error: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code} - {response.text}")
                
            results.append({
                "test_case": test_case,
                "status_code": response.status_code,
                "response": response.json() if response.status_code == 200 else response.text,
                "processing_time": end_time - start_time
            })
            
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Flask server not running on localhost:8000")
            results.append({
                "test_case": test_case,
                "error": "Connection failed - server not running"
            })
        except Exception as e:
            print(f"❌ Request Error: {e}")
            results.append({
                "test_case": test_case,
                "error": str(e)
            })
        
        time.sleep(1)
    
    return results

def test_frontend_integration():
    """Test the frontend integration"""
    print("\n🖥️  Testing Frontend Integration")
    print("=" * 50)
    
    base_url = "http://localhost:3000"
    endpoint = "/api/translate"
    
    test_case = {
        "action": "translateWithOdiaGenAI",
        "text": "Welcome to our financial advisory service.",
        "sourceLang": "en",
        "targetLanguage": "or"
    }
    
    try:
        print(f"📝 Testing frontend API: {test_case['text']}")
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}{endpoint}",
            json=test_case,
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Frontend Success: {data.get('translated_text', 'No translation')}")
                print(f"⏱️  Time: {end_time - start_time:.2f}s")
            else:
                print(f"❌ Frontend Error: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code} - {response.text}")
            
        return {
            "status_code": response.status_code,
            "response": response.json() if response.status_code == 200 else response.text,
            "processing_time": end_time - start_time
        }
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Frontend server not running on localhost:3000")
        return {"error": "Connection failed - frontend server not running"}
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return {"error": str(e)}

def check_environment():
    """Check if the required environment is set up"""
    print("🔧 Checking Environment Setup")
    print("=" * 50)
    
    # Check for Hugging Face API token
    hf_token = os.getenv('HUGGINGFACE_API_TOKEN')
    if hf_token:
        print("✅ HUGGINGFACE_API_TOKEN is set")
    else:
        print("⚠️  HUGGINGFACE_API_TOKEN not found - OdiaGenAI may not work properly")
        print("   Set it with: export HUGGINGFACE_API_TOKEN=your_token_here")
    
    # Check if services directory exists
    services_dir = os.path.join(os.path.dirname(__file__), 'services')
    if os.path.exists(services_dir):
        print("✅ Services directory found")
        
        # Check if OdiaGenAI service file exists
        odiagenai_file = os.path.join(services_dir, 'odiagenai_service.py')
        if os.path.exists(odiagenai_file):
            print("✅ OdiaGenAI service file found")
        else:
            print("❌ OdiaGenAI service file not found")
    else:
        print("❌ Services directory not found")
    
    return hf_token is not None

def main():
    """Main test function"""
    print("🌟 OdiaGenAI Integration Test Suite")
    print("=" * 60)
    
    # Check environment
    env_ok = check_environment()
    
    if not env_ok:
        print("\n⚠️  Environment setup incomplete. Some tests may fail.")
        print("Please ensure HUGGINGFACE_API_TOKEN is set for full functionality.")
    
    # Test the service directly
    service_results = test_odiagenai_service()
    
    # Test Flask endpoint
    flask_results = test_flask_endpoint()
    
    # Test frontend integration
    frontend_result = test_frontend_integration()
    
    # Final summary
    print("\n🎯 Final Test Summary")
    print("=" * 60)
    
    if service_results:
        successful_service = sum(1 for r in service_results if r['result']['success'])
        print(f"🔧 Direct Service: {successful_service}/{len(service_results)} tests passed")
    else:
        print("🔧 Direct Service: Failed to run tests")
    
    if flask_results:
        successful_flask = sum(1 for r in flask_results if r.get('status_code') == 200)
        print(f"🌐 Flask Endpoint: {successful_flask}/{len(flask_results)} tests passed")
    else:
        print("🌐 Flask Endpoint: No tests run")
    
    if frontend_result and frontend_result.get('status_code') == 200:
        print("🖥️  Frontend Integration: ✅ Passed")
    else:
        print("🖥️  Frontend Integration: ❌ Failed")
    
    print("\n💡 Next Steps:")
    print("1. Ensure Flask server is running: python full_code.py")
    print("2. Ensure Next.js frontend is running: npm run dev")
    print("3. Set HUGGINGFACE_API_TOKEN for full OdiaGenAI functionality")
    print("4. Test the Oriya page at http://localhost:3000/oriya")

if __name__ == "__main__":
    main()
