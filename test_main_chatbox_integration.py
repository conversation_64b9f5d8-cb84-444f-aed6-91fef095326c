#!/usr/bin/env python3
"""
Test the main ChatBox integration by testing both endpoints
"""

import requests
import json

def test_financial_query_endpoint():
    """Test the /financial_query endpoint used by main ChatBox"""
    print("🧪 Testing /financial_query endpoint (Main ChatBox)")
    print("-" * 50)
    
    test_cases = [
        {
            "query": "பங்குச் சந்தையின் தற்போதைய நிலை என்ன?",
            "language": "Tamil",
            "target_language": "ta"
        },
        {
            "query": "స్టాక్ మార్కెట్ ప్రస్తుత స్థితి ఎలా ఉంది?",
            "language": "Telugu",
            "target_language": "te"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['language']}")
        print(f"Query: {test_case['query']}")
        print("-" * 30)
        
        try:
            response = requests.post(
                "http://localhost:5010/financial_query",
                json={
                    "query": test_case['query'],
                    "language": test_case['language'],
                    "target_language": test_case['target_language'],
                    "enable_translation": True,
                    "index_name": "default"
                },
                headers={"Content-Type": "application/json"},
                timeout=45
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Status: SUCCESS")
                print(f"💬 AI Response Preview: {data.get('ai_response', '')[:100]}...")
                print(f"❓ Related Questions: {len(data.get('related_questions', []))}")
                print(f"📄 Sentence Analysis: {len(data.get('sentence_analysis', []))}")
            else:
                print(f"❌ Status: FAILED ({response.status_code})")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_multilingual_endpoint():
    """Test the /api/multilingual_financial_query endpoint"""
    print("\n\n🧪 Testing /api/multilingual_financial_query endpoint")
    print("-" * 50)
    
    test_cases = [
        {
            "query": "பங்குச் சந்தையின் தற்போதைய நிலை என்ன?",
            "language": "Tamil"
        },
        {
            "query": "స్టాక్ మార్కెట్ ప్రస్తుత స్థితి ఎలా ఉంది?",
            "language": "Telugu"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['language']}")
        print(f"Query: {test_case['query']}")
        print("-" * 30)
        
        try:
            response = requests.post(
                "http://localhost:5010/api/multilingual_financial_query",
                json={"query": test_case['query']},
                headers={"Content-Type": "application/json"},
                timeout=45
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Status: SUCCESS")
                print(f"🌍 Language: {data.get('processing_language', 'Unknown')}")
                print(f"💬 AI Response Preview: {data.get('ai_response', '')[:100]}...")
                print(f"❓ Related Questions: {len(data.get('related_questions', []))}")
                print(f"📄 Sentiment Analysis: {len(data.get('sentiment_analysis', []))}")
            else:
                print(f"❌ Status: FAILED ({response.status_code})")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    print("🚀 Testing UI Integration - Both Endpoints")
    print("=" * 60)
    
    # Test both endpoints
    test_financial_query_endpoint()
    test_multilingual_endpoint()
    
    print("\n" + "=" * 60)
    print("📊 INTEGRATION ANALYSIS")
    print("=" * 60)
    print("1. Main ChatBox uses /financial_query endpoint")
    print("2. Dedicated language pages use /api/multilingual_financial_query endpoint")
    print("3. Both should work with consolidated backend on port 5010")
    print("\n🎯 Recommendation: Consider updating main ChatBox to use")
    print("   /api/multilingual_financial_query for consistency")

if __name__ == "__main__":
    main()
